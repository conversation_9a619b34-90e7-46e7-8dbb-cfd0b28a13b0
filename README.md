# 生成式人工智能辅助编程教学系统

这是一个集成了生成式人工智能功能的在线程教学平台，旨在为教师和学生提供更加个性化和高效的教学体验。

## 功能特点

### 学生端功能
- 用户注册和登录（支持密码加密）
- 课程浏览和搜索
- 课程收藏和评分
- 课程评论和互动
- 个人学习进度跟踪
- 个性化学习报告和建议

### 教师端功能
- 课程管理（创建、编辑、删除）
- 课程模块和章节管理
- 知识点管理系统
- 学生评论管理
- 教学效果评估报告
- 学生学习数据分析

### 数据分析功能
- 知识点掌握度评估
- 学习进度跟踪与分析
- 个性化学习报告生成
- 教学效果评估
- 基于知识点的课程推荐

## 系统架构

### 前端
- 学生端：Vue.js + Element UI
- 教师端：Vue.js + Element UI

### 后端
- Web框架：Flask
- 数据库：MySQL
- ORM：SQLAlchemy
- 认证：JWT Token

## 数据模型

系统包含以下核心数据模型：
- 用户（学生/教师）
- 课程
- 课程模块
- 知识点
- 学习活动
- 学习进度
- 评论
- 评分

## 安装和配置

### 环境要求
- Python 3.8+
- MySQL 5.7+
- Node.js 12+

### 安装步骤

1. 克隆项目
```bash
git clone https://github.com/Hyntaox/GenAIPTS.git
cd GenAIPTS
```

2. 安装后端依赖
```bash
pip install -r requirements.txt
```

3. 配置数据库
```bash
# 修改数据库连接配置
# 打开config.py文件，根据您的MySQL环境修改以下参数：
DB_HOST = 'localhost'    # 数据库服务器地址
DB_PORT = 3306           # 数据库端口
DB_USER = 'root'         # 数据库用户名
DB_PASSWORD = '123456'   # 数据库密码
DB_NAME = 'GenAIPTS' # 数据库名称

# 系统启动时会自动检查并创建数据库（如果不存在）
```

4. 初始化数据库（添加模拟数据）
```bash
# 方法一：使用命令行导入SQL脚本
# Windows系统
mysql -u用户名 -p密码 -D数据库名 < data_initialization_complete.sql

# 示例（使用默认配置）：
mysql -uroot -p123456 -DGenAIPTS < data_initialization_complete.sql

# 如果MySQL bin目录不在PATH中，需要指定完整路径：
"C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql" -uroot -p123456 -DGenAIPTS < data_initialization_complete.sql

# Linux/Mac系统
mysql -u用户名 -p密码 -D数据库名 < data_initialization_complete.sql

# 方法二：使用MySQL客户端
# 1. 登录MySQL客户端
mysql -uroot -p123456

# 2. 选择数据库
USE GenAIPTS;

# 3. 导入SQL脚本
source data_initialization_complete.sql
```

**初始化数据说明**：
- `data_initialization_complete.sql`包含完整的模拟数据，包括用户、课程、知识点等
- 默认管理员账号：admin1，密码：123456
- 默认学生账号：student1，密码：123456
- 导入后系统将包含示例课程、知识点和学习数据，便于功能演示

5. 安装前端依赖
```bash
# 学生端
cd fronted/student
npm install

# 教师端
cd fronted/admin
npm install
```

6. 启动应用
```bash
# 启动后端
python app.py

# 启动学生端前端（新终端）
cd fronted/student
npm run serve

# 启动教师端前端（新终端）
cd fronted/admin
npm run serve
```

### Git忽略文件说明

本项目使用`.gitignore`文件来避免将不必要的文件提交到版本控制系统中。以下是被忽略的主要文件类型：

- Python缓存文件和虚拟环境
- 前端构建文件和依赖
- 数据库文件
- 日志文件
- 上传的文件
- IDE配置文件

#### 首次克隆项目后恢复被忽略的必要文件

由于`.gitignore`文件的存在，一些必要的目录和文件可能不会被包含在Git仓库中。以下是恢复这些文件的步骤：

1. **创建必要的目录**
```bash
# 创建上传文件目录
mkdir -p uploads

# 创建日志目录
mkdir -p logs
```

2. **恢复前端依赖**
```bash
# 安装学生端依赖
cd fronted/student
npm install

# 安装教师端依赖
cd ../admin
npm install
```

3. **创建Python虚拟环境**
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境（Windows）
venv\Scripts\activate

# 激活虚拟环境（Linux/Mac）
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

4. **初始化数据库**
按照上述"初始化数据库"部分的说明操作，确保数据库正确设置。

## 使用指南

### 学生端
1. 访问 http://localhost:8080
2. 注册/登录账号
3. 浏览和选择课程
4. 学习课程内容并完成相关活动
5. 查看个人学习报告和进度

### 教师端
1. 访问 http://localhost:8081
2. 使用管理员账号登录
3. 管理课程和教学内容
4. 查看学生学习数据和教学效果报告
5. 管理知识点和课程关联

### 代码分析工具
1. 启动代码分析模块
```bash
# 进入代码分析目录
cd code_analysis

# 启动代码分析服务
uvicorn backend.app:app --reload 
```
2. 访问代码分析界面（也可通过学生端个人中心访问）：http://localhost:8000/ui
3. 上传代码文件或输入代码片段
4. 查看分析结果和改进建议
5. 更多使用指南与功能详情请参考代码分析模块的README文档（例如api配置）

## 知识点管理系统

知识点管理是本系统的核心特色，它允许：
1. 创建和管理知识点
2. 将知识点关联到课程
3. 跟踪学生对知识点的掌握程度
4. 基于知识点生成学习报告和教学建议

## 开发者指南

### 项目结构
```
online_teach/
├── app.py                 # 应用入口
├── config.py              # 配置文件
├── myapp/                 # 后端代码
│   ├── __init__.py
│   ├── admin/             # 管理员API
│   ├── api/               # 公共API
│   ├── data_analysis/     # 数据分析模块
│   ├── front/             # 前端API
│   └── models.py          # 数据模型
├── fronted/               # 前端代码
│   ├── admin/             # 教师端
│   └── student/           # 学生端
├── .gitignore             # Git忽略文件配置
└── requirements.txt       # 依赖列表
```

### API文档
系统提供以下主要API：
- 用户认证API
- 课程管理API
- 知识点管理API
- 学习数据分析API
- 教学评估API

## 核心设计文档

### 系统架构设计

本系统采用前后端分离的架构，主要分为三个核心部分：

1. **前端应用**
   - 学生端：基于Vue.js的SPA应用，负责学生的课程学习和进度跟踪
   - 教师端：基于Vue.js的SPA应用，负责教师的课程管理和教学分析
   - 共享组件：两端共享的UI组件和工具函数

2. **后端服务**
   - API服务：基于Flask的RESTful API，提供数据访问和业务逻辑
   - 数据分析模块：处理学习数据和生成分析报告
   - 认证服务：处理用户认证和权限控制

3. **代码分析模块**
   - 静态分析引擎：基于AST的代码静态分析
   - 安全规则库：包含常见安全漏洞的检测规则
   - AI增强分析：结合大模型进行深度代码理解和分析

### 数据流设计

系统的主要数据流如下：

1. **学习数据流**
   - 学生学习课程 → 记录学习活动 → 更新知识点掌握度 → 生成学习报告
   - 教师查看报告 → 调整教学内容 → 更新课程和知识点

2. **知识点管理流**
   - 教师创建知识点 → 关联到课程 → 学生学习相关内容 → 系统评估掌握度
   - 系统分析知识点关联 → 生成知识图谱 → 提供个性化学习建议

3. **代码分析流**
   - 用户上传代码 → 静态分析引擎处理 → 应用安全规则检测 → AI模型深度分析
   - 生成分析报告 → 提供修复建议 → 用户改进代码

### 知识点系统设计

知识点系统是本平台的核心创新，其设计原则如下：

1. **知识点粒度**：知识点设计为最小可教学单元，便于精确跟踪学习进度
2. **多维关联**：知识点可以与多个课程关联，形成网状知识结构
3. **进度量化**：学习进度通过0-1的浮点数精确量化，便于数据分析
4. **自适应学习**：基于知识点掌握度，系统可以推荐个性化学习路径

### 安全设计

系统的安全设计包括：

1. **用户认证**：基于JWT的无状态认证，支持令牌刷新和失效机制
2. **密码安全**：使用PBKDF2-SHA256算法进行密码哈希，防止彩虹表攻击
3. **权限控制**：基于角色的访问控制，严格区分学生和教师权限
4. **数据保护**：敏感数据加密存储，API访问限流和防护

### 扩展性设计

系统设计考虑了未来的扩展性：

1. **模块化架构**：各功能模块松耦合，便于独立升级和扩展
2. **API版本控制**：支持API版本管理，便于平滑升级
3. **插件系统**：代码分析模块支持插件扩展，可添加新的分析规则
4. **多语言支持**：界面和内容支持国际化，便于全球化部署

## 许可证

MIT License
