import json
from datetime import datetime, timedelta
from myapp.models import User, Course, LearningActivity, LearningProgress, KnowledgePoint, Scores

def generate_personal_report(user_id):
    """生成个人学习报告"""
    user = User.query.get(user_id)
    if not user:
        return None

    # 获取用户的课程
    courses = user.courses

    # 获取用户的学习活动
    activities = LearningActivity.query.filter_by(user_id=user_id).all()

    # 计算总学习时长（小时）
    total_hours = sum(a.duration or 0 for a in activities) / 3600

    # 计算课程完成率
    completed_courses = set()
    for activity in activities:
        if activity.activity_type == 'complete':
            completed_courses.add(activity.course_id)

    completion_rate = len(completed_courses) / len(courses) * 100 if courses else 0

    # 获取知识点掌握情况
    progress_records = LearningProgress.query.filter_by(user_id=user_id).all()
    knowledge_points = {}
    for record in progress_records:
        kp = record.knowledge_point
        if kp.id not in knowledge_points:
            knowledge_points[kp.id] = {
                'name': kp.name,
                'mastery_level': record.mastery_level,
                'count': 1
            }
        else:
            knowledge_points[kp.id]['mastery_level'] += record.mastery_level
            knowledge_points[kp.id]['count'] += 1

    # 计算平均掌握程度
    for kp_id in knowledge_points:
        knowledge_points[kp_id]['mastery_level'] /= knowledge_points[kp_id]['count']

    kp_list = [
        {
            'name': kp['name'],
            'mastery_level': kp['mastery_level']
        } for kp in knowledge_points.values()
    ]

    # 生成学习建议
    suggestions = generate_learning_suggestions(user_id, knowledge_points, activities)

    # 推荐课程
    recommended_courses = recommend_courses(user_id, knowledge_points)

    return {
        'totalCourses': len(courses),
        'totalHours': round(total_hours, 1),
        'completionRate': round(completion_rate, 1),
        'knowledgePoints': kp_list,
        'suggestions': suggestions,
        'recommendedCourses': recommended_courses
    }

def generate_learning_suggestions(user_id, knowledge_points, activities):
    """生成学习建议"""
    suggestions = []

    # 基于知识点掌握情况的建议
    weak_points = [kp for kp_id, kp in knowledge_points.items() if kp['mastery_level'] < 0.6]
    if weak_points:
        weak_names = ', '.join([kp['name'] for kp in weak_points[:3]])
        suggestions.append(f"建议加强对以下知识点的学习：{weak_names}")

    # 基于学习频率的建议
    recent_days = 7
    recent_date = datetime.now() - timedelta(days=recent_days)
    recent_activities = [a for a in activities if a.created_at >= recent_date]

    if len(recent_activities) < 3:
        suggestions.append(f"您最近{recent_days}天的学习频率较低，建议增加学习时间")

    # 基于学习时长的建议
    total_duration = sum(a.duration or 0 for a in recent_activities) / 3600  # 转换为小时
    if total_duration < 5:
        suggestions.append(f"您最近{recent_days}天的总学习时长为{round(total_duration, 1)}小时，建议每周至少学习5小时")

    # 如果没有生成任何建议，添加一个默认建议
    if not suggestions:
        suggestions.append("继续保持良好的学习习惯，建议尝试更多不同类型的课程")

    return suggestions

def recommend_courses(user_id, knowledge_points):
    """推荐课程"""
    user = User.query.get(user_id)
    if not user:
        return []

    # 获取用户已学习的课程ID
    learned_course_ids = [c.id for c in user.courses]

    # 找出用户掌握程度较低的知识点
    weak_point_ids = [kp_id for kp_id, kp in knowledge_points.items() if kp['mastery_level'] < 0.6]

    # 查找包含这些知识点但用户尚未学习的课程
    recommended = []
    if weak_point_ids:
        for kp_id in weak_point_ids:
            kp = KnowledgePoint.query.get(kp_id)
            for course in kp.courses:
                if course.id not in learned_course_ids and len(recommended) < 5:
                    recommended.append({
                        'id': course.id,
                        'name': course.name,
                        'cover': course.cover,
                        'description': f"推荐理由：帮助提高{kp.name}的掌握程度"
                    })

    # 如果推荐数量不足，添加一些热门课程
    if len(recommended) < 5:
        popular_courses = Course.query.order_by(Course.view_num.desc()).limit(10).all()
        for course in popular_courses:
            if course.id not in learned_course_ids and not any(r['id'] == course.id for r in recommended) and len(recommended) < 5:
                recommended.append({
                    'id': course.id,
                    'name': course.name,
                    'cover': course.cover,
                    'description': "推荐理由：热门课程"
                })

    return recommended

def generate_teaching_report(admin, courses, activities, scores):
    """生成教学效果评估报告"""
    try:
        if not courses:
            print("没有课程数据，无法生成教学报告")
            return None

        # 计算基本统计数据
        total_courses = len(courses)

        # 获取学习这些课程的学生数量
        student_ids = set()
        for activity in activities:
            if hasattr(activity, 'user_id') and activity.user_id:
                student_ids.add(activity.user_id)
        total_students = len(student_ids)

        # 计算平均评分
        try:
            total_score = sum(s.score for s in scores if hasattr(s, 'score') and s.score is not None)
            average_score = total_score / len(scores) if scores else 0
        except Exception as e:
            print(f"计算平均评分时出错: {e}")
            average_score = 0

        # 课程参与度数据
        course_engagement = []
        for course in courses:
            try:
                course_activities = [a for a in activities if hasattr(a, 'course_id') and a.course_id == course.id]
                views = len([a for a in course_activities if hasattr(a, 'activity_type') and a.activity_type == 'view'])
                completions = len([a for a in course_activities if hasattr(a, 'activity_type') and a.activity_type == 'complete'])

                course_engagement.append({
                    'name': course.name,
                    'views': views,
                    'completions': completions
                })
            except Exception as e:
                print(f"处理课程{course.id}的参与度数据时出错: {e}")
                # 添加一个默认值，避免数据缺失
                course_engagement.append({
                    'name': course.name,
                    'views': 0,
                    'completions': 0
                })

        # 学生掌握情况
        student_mastery = {
            'excellent': 0,  # >80%
            'good': 0,       # 60-80%
            'average': 0,    # 40-60%
            'needImprovement': 0  # <40%
        }

        for student_id in student_ids:
            try:
                # 计算该学生对所有课程的平均掌握程度
                student_scores = [s.score for s in scores if hasattr(s, 'user_id') and hasattr(s, 'score') and s.user_id == student_id and s.score is not None]
                avg_score = sum(student_scores) / len(student_scores) if student_scores else 0
                mastery_level = avg_score / 5  # 假设评分是1-5

                if mastery_level > 0.8:
                    student_mastery['excellent'] += 1
                elif mastery_level > 0.6:
                    student_mastery['good'] += 1
                elif mastery_level > 0.4:
                    student_mastery['average'] += 1
                else:
                    student_mastery['needImprovement'] += 1
            except Exception as e:
                print(f"处理学生{student_id}的掌握情况时出错: {e}")
                # 出错时不计入任何类别

        # 评分分布
        score_distribution = {}
        for score in range(1, 6):  # 假设评分是1-5
            try:
                count = len([s for s in scores if hasattr(s, 'score') and s.score == score])
                score_distribution[str(score)] = count
            except Exception as e:
                print(f"计算评分{score}的分布时出错: {e}")
                score_distribution[str(score)] = 0

        # 生成改进建议
        try:
            suggestions = generate_teaching_suggestions(courses, activities, scores)
        except Exception as e:
            print(f"生成教学建议时出错: {e}")
            suggestions = [{
                'title': '继续保持',
                'content': "您的课程整体表现良好，建议继续保持并尝试添加更多互动环节"
            }]

        return {
            'totalCourses': total_courses,
            'totalStudents': total_students,
            'averageScore': round(average_score, 1),
            'courseEngagement': course_engagement,
            'studentMastery': student_mastery,
            'scoreDistribution': score_distribution,
            'suggestions': suggestions
        }
    except Exception as e:
        print(f"生成教学报告时发生错误: {e}")
        # 返回一个基本的报告结构，避免返回None
        return {
            'totalCourses': len(courses) if courses else 0,
            'totalStudents': 0,
            'averageScore': 0,
            'courseEngagement': [],
            'studentMastery': {
                'excellent': 0,
                'good': 0,
                'average': 0,
                'needImprovement': 0
            },
            'scoreDistribution': {},
            'suggestions': [{
                'title': '数据处理错误',
                'content': "生成报告时发生错误，请稍后再试或联系管理员"
            }]
        }

def generate_teaching_suggestions(courses, activities, scores):
    """生成教学改进建议"""
    try:
        suggestions = []

        # 分析低参与度课程
        course_participation = {}
        for activity in activities:
            if hasattr(activity, 'course_id') and activity.course_id:
                if activity.course_id not in course_participation:
                    course_participation[activity.course_id] = 1
                else:
                    course_participation[activity.course_id] += 1

        low_participation_courses = []
        for course in courses:
            if hasattr(course, 'id') and course.id:
                participation = course_participation.get(course.id, 0)
                if participation < 10:  # 假设参与度低于10次为低参与度
                    low_participation_courses.append(course.name)

        if low_participation_courses:
            course_names = ', '.join(low_participation_courses[:3])
            suggestions.append({
                'title': '提高课程参与度',
                'content': f"以下课程参与度较低，建议改进内容或增加互动环节：{course_names}"
            })

        # 分析低评分课程
        course_scores = {}
        for score in scores:
            if hasattr(score, 'course_id') and hasattr(score, 'score') and score.course_id and score.score is not None:
                if score.course_id not in course_scores:
                    course_scores[score.course_id] = [score.score]
                else:
                    course_scores[score.course_id].append(score.score)

        low_score_courses = []
        for course in courses:
            if hasattr(course, 'id') and course.id:
                course_scores_list = course_scores.get(course.id, [])
                if course_scores_list:
                    course_avg_score = sum(course_scores_list) / len(course_scores_list)
                    if course_avg_score < 3:  # 假设平均评分低于3分为低评分
                        low_score_courses.append(course.name)

        if low_score_courses:
            course_names = ', '.join(low_score_courses[:3])
            suggestions.append({
                'title': '提高课程质量',
                'content': f"以下课程评分较低，建议检查内容质量并收集学生反馈：{course_names}"
            })

        # 如果没有生成任何建议，添加一个默认建议
        if not suggestions:
            suggestions.append({
                'title': '继续保持',
                'content': "您的课程整体表现良好，建议继续保持并尝试添加更多互动环节"
            })

        return suggestions
    except Exception as e:
        print(f"生成教学建议时发生错误: {e}")
        # 返回一个默认建议
        return [{
            'title': '继续保持',
            'content': "您的课程整体表现良好，建议继续保持并尝试添加更多互动环节"
        }]
