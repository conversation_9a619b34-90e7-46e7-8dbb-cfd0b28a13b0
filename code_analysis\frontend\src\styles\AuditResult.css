.ai-analysis-container {
  margin-top: 20px;
  padding: 10px;
  background-color: #f0f8ff;
  border-radius: 8px;
  border: 1px solid #d1e7ff;
}

.ai-analysis-container h4 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-weight: 600;
}

.ai-analysis {
  margin-top: 10px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 4px solid #3498db;
}

.ai-analysis h3 {
  color: #2c3e50;
  margin-bottom: 15px;
}

.ai-analysis .section {
  margin-bottom: 20px;
}

.ai-analysis h4 {
  color: #34495e;
  margin-bottom: 10px;
}

.ai-analysis ul {
  list-style: none;
  padding-left: 0;
}

.ai-analysis li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.ai-analysis li:before {
  content: "•";
  color: #3498db;
  font-weight: bold;
  margin-right: 8px;
}

.ai-analysis.error {
  background-color: #fff3f3;
  border: 1px solid #ffcdd2;
  padding: 15px;
  margin-top: 20px;
  border-radius: 4px;
}

.ai-analysis.error h3 {
  color: #d32f2f;
}

.ai-analysis.error pre {
  background-color: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  margin-top: 10px;
}

.ai-analysis .debug-info {
  margin-top: 20px;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.ai-analysis .debug-info pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  font-size: 12px;
  margin: 0;
}

.issue-item {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #3498db;
}

.issue-item h5 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.audit-result {
  padding: 20px;
}

.audit-summary {
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.audit-summary h2 {
  color: #2c3e50;
  margin-bottom: 15px;
}

.audit-summary ul {
  list-style: none;
  padding: 0;
}

.audit-summary li {
  margin-bottom: 10px;
}

.suspicious-file, .analyzed-file {
  background-color: #fff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.suspicious-file h3, .analyzed-file h3 {
  color: #2c3e50;
  margin-bottom: 15px;
}

.issue {
  margin-bottom: 15px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.severity {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: bold;
  margin-bottom: 10px;
}

.severity.high {
  background-color: #dc3545;
  color: white;
}

.severity.medium {
  background-color: #ffc107;
  color: black;
}

.severity.low {
  background-color: #28a745;
  color: white;
}

/* Python静态分析样式 */
.python-static-analysis {
  margin-top: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.python-static-analysis h3 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-weight: 600;
}

.analysis-summary {
  background-color: #e3f2fd;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 20px;
  border-left: 4px solid #2196f3;
}

.analysis-summary p {
  margin: 5px 0;
  color: #1565c0;
  font-weight: 500;
}

.auto-fix-available {
  color: #388e3c !important;
  font-weight: 600;
}

/* 标签页样式 */
.analysis-tabs {
  margin-top: 20px;
}

.tab-headers {
  display: flex;
  border-bottom: 2px solid #e9ecef;
  margin-bottom: 20px;
}

.tab-header {
  background: none;
  border: none;
  padding: 12px 20px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #6c757d;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
}

.tab-header:hover {
  color: #495057;
  background-color: #f8f9fa;
}

.tab-header.active {
  color: #2196f3;
  border-bottom-color: #2196f3;
  background-color: #fff;
}

.tab-content {
  min-height: 200px;
}

/* 工具头部样式 */
.tool-header {
  background-color: #fff;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 20px;
  border: 1px solid #e9ecef;
}

.tool-header h4 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-weight: 600;
}

.tool-header p {
  margin: 5px 0;
  color: #6c757d;
}

.auto-fix-info {
  color: #388e3c !important;
  font-weight: 500;
}

.pylint-score {
  color: #ff9800 !important;
  font-weight: 600;
}

/* 问题列表样式 */
.issues-list {
  margin-bottom: 20px;
}

.no-issues {
  text-align: center;
  color: #6c757d;
  font-style: italic;
  padding: 40px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.issue-item {
  background-color: #fff;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 15px;
  transition: box-shadow 0.3s ease;
}

.issue-item:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.issue-item.severity-high {
  border-left: 4px solid #dc3545;
}

.issue-item.severity-medium {
  border-left: 4px solid #ffc107;
}

.issue-item.severity-low {
  border-left: 4px solid #28a745;
}

.issue-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
  flex-wrap: wrap;
}

.severity-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.severity-badge.high {
  background-color: #dc3545;
  color: white;
}

.severity-badge.medium {
  background-color: #ffc107;
  color: #212529;
}

.severity-badge.low {
  background-color: #28a745;
  color: white;
}

.issue-type {
  background-color: #e9ecef;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #495057;
}

.line-number {
  background-color: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #6c757d;
  font-family: monospace;
}

.fixable-badge {
  background-color: #d4edda;
  color: #155724;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.message-id {
  background-color: #fff3cd;
  color: #856404;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-family: monospace;
}

.issue-description {
  margin: 10px 0 5px 0;
  color: #495057;
  line-height: 1.5;
}

.issue-code, .issue-symbol {
  margin: 5px 0;
  font-size: 13px;
  color: #6c757d;
  font-family: monospace;
}

/* Pylint统计信息样式 */
.pylint-stats {
  background-color: #fff;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 20px;
  border: 1px solid #e9ecef;
}

.pylint-stats h5 {
  color: #2c3e50;
  margin-bottom: 15px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.stat-label {
  color: #6c757d;
  font-size: 14px;
}

.stat-value {
  font-weight: 600;
  color: #2c3e50;
  font-size: 16px;
}

/* 修复建议样式 */
.fixes-section {
  background-color: #d4edda;
  padding: 15px;
  border-radius: 6px;
  margin-top: 20px;
  border: 1px solid #c3e6cb;
}

.fixes-section h5 {
  color: #155724;
  margin-bottom: 15px;
}

.fix-item {
  background-color: #fff;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 10px;
  border: 1px solid #c3e6cb;
}

.fix-description {
  color: #155724;
  margin-bottom: 10px;
  font-weight: 500;
}

.fix-diff {
  background-color: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
  border: 1px solid #e9ecef;
}

/* 改进建议样式 */
.recommendations {
  background-color: #fff3cd;
  padding: 15px;
  border-radius: 6px;
  margin-top: 20px;
  border: 1px solid #ffeaa7;
}

.recommendations h4 {
  color: #856404;
  margin-bottom: 15px;
}

.recommendations ul {
  list-style: none;
  padding: 0;
}

.recommendations li {
  margin-bottom: 10px;
  padding-left: 20px;
  position: relative;
  color: #856404;
  line-height: 1.5;
}

.recommendations li:before {
  content: "💡";
  position: absolute;
  left: 0;
  top: 0;
}