# Python
__pycache__/
*/__pycache__/
migrations/__pycache__/
*.py[cod]
*$py.class
*.so
.Python
venv/
env/
ENV/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Frontend
fronted/*/node_modules/
fronted/*/dist/
fronted/*/.env.local
fronted/*/.env.*.local

# Database
*.sqlite3
*.db
*.sql
!data_initialization_complete.sql
!clear_data.sql

# Uploads
uploads/

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store
Thumbs.db

# Docker
.dockerignore
docker-compose.override.yml

# Misc
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.coverage
htmlcov/
.pytest_cache/
.tox/