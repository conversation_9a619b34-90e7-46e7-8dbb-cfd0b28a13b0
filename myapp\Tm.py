from app import db

ass = db.Table('ass',
                             db.<PERSON><PERSON><PERSON>('teacher_id',db.<PERSON><PERSON>,db.<PERSON>('teacher.id')),
                             db.<PERSON><PERSON>('student_id', db.<PERSON><PERSON><PERSON>, db.<PERSON>('student.id'))
                             )


class Student(db.Model):
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    name = db.<PERSON>umn(db.String(255),nullable=False)
    teachers = db.relationship('Teacher',
                               secondary=ass)


class Teacher(db.Model):
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    name = db.<PERSON>umn(db.String(255),nullable=False)
    students = db.relationship('Student', secondary=ass)