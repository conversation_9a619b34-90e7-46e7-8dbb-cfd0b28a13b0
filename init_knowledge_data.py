from myapp import create_app, db
from myapp.models import Module, KnowledgePoint
from datetime import datetime, timedelta

# 创建应用实例
app = create_app()

# 在应用上下文中运行
with app.app_context():
    # 检查是否已有模块数据
    existing_modules = Module.query.all()
    if not existing_modules:
        print("添加模块数据...")
        modules = [
            <PERSON><PERSON><PERSON>(name="Python编程"),
            <PERSON><PERSON><PERSON>(name="Web开发"),
            <PERSON><PERSON><PERSON>(name="数据库"),
            <PERSON><PERSON><PERSON>(name="数据结构"),
            <PERSON><PERSON><PERSON>(name="算法"),
            <PERSON><PERSON><PERSON>(name="机器学习"),
            <PERSON><PERSON><PERSON>(name="网络安全"),
            <PERSON><PERSON><PERSON>(name="Java开发")
        ]
        db.session.add_all(modules)
        db.session.commit()
        print(f"成功添加{len(modules)}个模块")
    else:
        print(f"已存在{len(existing_modules)}个模块，跳过添加")
        modules = existing_modules

    # 检查是否已有知识点数据
    existing_kps = KnowledgePoint.query.all()
    if not existing_kps:
        print("添加知识点数据...")
        now = datetime.now()
        knowledge_points = [
            # Python编程模块的知识点
            KnowledgePoint(name="变量与数据类型", description="Python中的变量定义和基本数据类型", module_id=1, created_at=now - timedelta(days=90)),
            KnowledgePoint(name="条件语句", description="if-else条件判断和逻辑运算", module_id=1, created_at=now - timedelta(days=85)),
            KnowledgePoint(name="循环结构", description="for和while循环的使用方法", module_id=1, created_at=now - timedelta(days=80)),
            KnowledgePoint(name="函数与方法", description="函数定义、参数传递和返回值", module_id=1, created_at=now - timedelta(days=75)),
            
            # Web开发模块的知识点
            KnowledgePoint(name="HTML基础", description="HTML标签和文档结构", module_id=2, created_at=now - timedelta(days=70)),
            KnowledgePoint(name="CSS样式", description="CSS选择器和样式属性", module_id=2, created_at=now - timedelta(days=65)),
            KnowledgePoint(name="JavaScript", description="JavaScript语法和DOM操作", module_id=2, created_at=now - timedelta(days=60)),
            
            # 数据库模块的知识点
            KnowledgePoint(name="SQL基础", description="SQL查询语言基础", module_id=3, created_at=now - timedelta(days=55)),
            KnowledgePoint(name="数据库设计", description="数据库表结构设计和关系模型", module_id=3, created_at=now - timedelta(days=50)),
            KnowledgePoint(name="事务处理", description="数据库事务ACID特性", module_id=3, created_at=now - timedelta(days=45)),
            
            # 数据结构模块的知识点
            KnowledgePoint(name="数组", description="数组的定义和操作", module_id=4, created_at=now - timedelta(days=40)),
            KnowledgePoint(name="链表", description="单链表和双链表", module_id=4, created_at=now - timedelta(days=35)),
            KnowledgePoint(name="栈和队列", description="栈和队列的实现和应用", module_id=4, created_at=now - timedelta(days=30)),
            KnowledgePoint(name="树", description="二叉树和平衡树", module_id=4, created_at=now - timedelta(days=25)),
            KnowledgePoint(name="图", description="图的表示和遍历", module_id=4, created_at=now - timedelta(days=20)),
            
            # 算法模块的知识点
            KnowledgePoint(name="排序算法", description="常见排序算法及其复杂度分析", module_id=5, created_at=now - timedelta(days=15)),
            KnowledgePoint(name="搜索算法", description="二分查找和哈希查找", module_id=5, created_at=now - timedelta(days=10)),
            KnowledgePoint(name="动态规划", description="动态规划问题求解方法", module_id=5, created_at=now - timedelta(days=5))
        ]
        db.session.add_all(knowledge_points)
        db.session.commit()
        print(f"成功添加{len(knowledge_points)}个知识点")
    else:
        print(f"已存在{len(existing_kps)}个知识点，跳过添加")

    print("数据初始化完成！")
