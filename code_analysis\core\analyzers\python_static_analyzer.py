import logging
from typing import Dict, Any, List
from .pyflakes_analyzer import PyflakesAnalyzer
from .ruff_analyzer import RuffAnalyzer
from .pylint_analyzer import PylintAnalyzer

logger = logging.getLogger(__name__)

class PythonStaticAnalyzer:
    """Python静态分析管理器 - 集成Pyflakes、Ruff和Pylint"""
    
    def __init__(self):
        self.pyflakes = PyflakesAnalyzer()
        self.ruff = RuffAnalyzer()
        self.pylint = PylintAnalyzer()
        
    def analyze_file(self, file_path: str, auto_fix: bool = False) -> Dict[str, Any]:
        """分析单个Python文件"""
        try:
            if not file_path.endswith('.py'):
                return self._empty_result()
                
            logger.info(f"开始Python静态分析: {file_path}")
            
            # 1. Pyflakes - 基础逻辑错误
            logger.debug("运行Pyflakes分析...")
            pyflakes_issues = self.pyflakes.analyze_file(file_path)
            
            # 2. Ruff - 中高级逻辑错误+自动修复
            logger.debug("运行Ruff分析...")
            ruff_result = self.ruff.analyze_file(file_path, auto_fix)
            
            # 3. Pylint - 深度逻辑分析
            logger.debug("运行Pylint分析...")
            pylint_result = self.pylint.analyze_file(file_path)
            
            # 整合结果
            result = {
                'pyflakes': {
                    'tool_info': self.pyflakes.get_tool_info(),
                    'issues': pyflakes_issues,
                    'total_issues': len(pyflakes_issues)
                },
                'ruff': {
                    'tool_info': self.ruff.get_tool_info(),
                    'issues': ruff_result.get('issues', []),
                    'fixes': ruff_result.get('fixes', []),
                    'fixed_code': ruff_result.get('fixed_code'),
                    'total_issues': len(ruff_result.get('issues', []))
                },
                'pylint': {
                    'tool_info': self.pylint.get_tool_info(),
                    'issues': pylint_result.get('issues', []),
                    'score': pylint_result.get('score'),
                    'stats': pylint_result.get('stats', {}),
                    'total_issues': len(pylint_result.get('issues', []))
                },
                'summary': self._generate_summary(pyflakes_issues, ruff_result, pylint_result)
            }
            
            logger.info(f"Python静态分析完成: {file_path}")
            return result
            
        except Exception as e:
            logger.error(f"Python静态分析失败 {file_path}: {str(e)}")
            return self._error_result(str(e))
    
    def analyze_code(self, code: str, filename: str = "temp.py", auto_fix: bool = False) -> Dict[str, Any]:
        """分析代码字符串"""
        try:
            logger.info(f"开始Python代码静态分析: {filename}")
            
            # 1. Pyflakes - 基础逻辑错误
            logger.debug("运行Pyflakes分析...")
            pyflakes_issues = self.pyflakes.analyze_code(code, filename)
            
            # 2. Ruff - 中高级逻辑错误+自动修复
            logger.debug("运行Ruff分析...")
            ruff_result = self.ruff.analyze_code(code, filename, auto_fix)
            
            # 3. Pylint - 深度逻辑分析
            logger.debug("运行Pylint分析...")
            pylint_result = self.pylint.analyze_code(code, filename)
            
            # 整合结果
            result = {
                'pyflakes': {
                    'tool_info': self.pyflakes.get_tool_info(),
                    'issues': pyflakes_issues,
                    'total_issues': len(pyflakes_issues)
                },
                'ruff': {
                    'tool_info': self.ruff.get_tool_info(),
                    'issues': ruff_result.get('issues', []),
                    'fixes': ruff_result.get('fixes', []),
                    'fixed_code': ruff_result.get('fixed_code'),
                    'total_issues': len(ruff_result.get('issues', []))
                },
                'pylint': {
                    'tool_info': self.pylint.get_tool_info(),
                    'issues': pylint_result.get('issues', []),
                    'score': pylint_result.get('score'),
                    'stats': pylint_result.get('stats', {}),
                    'total_issues': len(pylint_result.get('issues', []))
                },
                'summary': self._generate_summary(pyflakes_issues, ruff_result, pylint_result)
            }
            
            logger.info(f"Python代码静态分析完成: {filename}")
            return result
            
        except Exception as e:
            logger.error(f"Python代码静态分析失败 {filename}: {str(e)}")
            return self._error_result(str(e))
    
    def _generate_summary(self, pyflakes_issues: List[Dict], ruff_result: Dict, pylint_result: Dict) -> Dict[str, Any]:
        """生成分析摘要"""
        try:
            total_issues = (
                len(pyflakes_issues) + 
                len(ruff_result.get('issues', [])) + 
                len(pylint_result.get('issues', []))
            )
            
            # 统计严重程度
            severity_counts = {'high': 0, 'medium': 0, 'low': 0, 'info': 0}
            
            # 统计pyflakes问题
            for issue in pyflakes_issues:
                severity = issue.get('severity', 'medium')
                if severity in severity_counts:
                    severity_counts[severity] += 1
            
            # 统计ruff问题
            for issue in ruff_result.get('issues', []):
                severity = issue.get('severity', 'medium')
                if severity in severity_counts:
                    severity_counts[severity] += 1
            
            # 统计pylint问题
            for issue in pylint_result.get('issues', []):
                severity = issue.get('severity', 'medium')
                if severity in severity_counts:
                    severity_counts[severity] += 1
            
            # 确定整体风险等级
            if severity_counts['high'] > 0:
                risk_level = 'high'
            elif severity_counts['medium'] > 0:
                risk_level = 'medium'
            elif severity_counts['low'] > 0:
                risk_level = 'low'
            else:
                risk_level = 'info'
            
            return {
                'total_issues': total_issues,
                'severity_counts': severity_counts,
                'risk_level': risk_level,
                'tools_used': ['pyflakes', 'ruff', 'pylint'],
                'pylint_score': pylint_result.get('score'),
                'auto_fixes_available': len(ruff_result.get('fixes', [])) > 0,
                'recommendations': self._generate_recommendations(pyflakes_issues, ruff_result, pylint_result)
            }
            
        except Exception as e:
            logger.error(f"生成摘要失败: {str(e)}")
            return {
                'total_issues': 0,
                'severity_counts': {'high': 0, 'medium': 0, 'low': 0, 'info': 0},
                'risk_level': 'unknown',
                'tools_used': [],
                'recommendations': []
            }
    
    def _generate_recommendations(self, pyflakes_issues: List[Dict], ruff_result: Dict, pylint_result: Dict) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        try:
            # 基于pyflakes结果的建议
            if pyflakes_issues:
                undefined_vars = [i for i in pyflakes_issues if i.get('type') == 'undefined_variable']
                if undefined_vars:
                    recommendations.append("修复未定义的变量，这可能导致运行时错误")
                
                unused_imports = [i for i in pyflakes_issues if i.get('type') == 'unused_import']
                if unused_imports:
                    recommendations.append("清理未使用的导入语句，提高代码整洁度")
            
            # 基于ruff结果的建议
            ruff_issues = ruff_result.get('issues', [])
            if ruff_issues:
                security_issues = [i for i in ruff_issues if i.get('type') == 'security']
                if security_issues:
                    recommendations.append("修复安全相关问题，提高代码安全性")
                
                if ruff_result.get('fixes'):
                    recommendations.append("使用Ruff的自动修复功能快速解决格式和简单逻辑问题")
            
            # 基于pylint结果的建议
            pylint_score = pylint_result.get('score')
            if pylint_score is not None:
                if pylint_score < 7.0:
                    recommendations.append("代码质量评分较低，建议重构以提高可维护性")
                elif pylint_score < 8.5:
                    recommendations.append("代码质量良好，可进一步优化以达到更高标准")
            
            pylint_issues = pylint_result.get('issues', [])
            if pylint_issues:
                errors = [i for i in pylint_issues if i.get('pylint_type') == 'error']
                if errors:
                    recommendations.append("优先修复Pylint检测到的错误，这些可能影响程序正确性")
        
        except Exception as e:
            logger.error(f"生成建议失败: {str(e)}")
        
        return recommendations
    
    def _empty_result(self) -> Dict[str, Any]:
        """返回空结果"""
        return {
            'pyflakes': {'tool_info': self.pyflakes.get_tool_info(), 'issues': [], 'total_issues': 0},
            'ruff': {'tool_info': self.ruff.get_tool_info(), 'issues': [], 'fixes': [], 'total_issues': 0},
            'pylint': {'tool_info': self.pylint.get_tool_info(), 'issues': [], 'score': None, 'stats': {}, 'total_issues': 0},
            'summary': {'total_issues': 0, 'severity_counts': {'high': 0, 'medium': 0, 'low': 0, 'info': 0}, 'risk_level': 'info', 'tools_used': [], 'recommendations': []}
        }
    
    def _error_result(self, error_msg: str) -> Dict[str, Any]:
        """返回错误结果"""
        error_issue = {
            'type': 'analysis_error',
            'line': 0,
            'description': f'分析失败: {error_msg}',
            'severity': 'error',
            'tool': 'python_static_analyzer'
        }
        
        return {
            'pyflakes': {'tool_info': self.pyflakes.get_tool_info(), 'issues': [error_issue], 'total_issues': 1},
            'ruff': {'tool_info': self.ruff.get_tool_info(), 'issues': [error_issue], 'fixes': [], 'total_issues': 1},
            'pylint': {'tool_info': self.pylint.get_tool_info(), 'issues': [error_issue], 'score': None, 'stats': {}, 'total_issues': 1},
            'summary': {'total_issues': 3, 'severity_counts': {'high': 0, 'medium': 0, 'low': 0, 'error': 3}, 'risk_level': 'error', 'tools_used': [], 'recommendations': ['修复分析工具配置问题']}
        }
