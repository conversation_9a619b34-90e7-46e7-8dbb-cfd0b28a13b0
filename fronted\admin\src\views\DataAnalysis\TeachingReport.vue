<template>
  <div class="teaching-report">
    <el-breadcrumb separator="/">
      <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>数据分析</el-breadcrumb-item>
      <el-breadcrumb-item>教学效果评估</el-breadcrumb-item>
    </el-breadcrumb>

    <el-card class="box-card" v-loading="loading">
      <div slot="header" class="clearfix">
        <span>教学效果评估报告</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="refreshData">刷新数据</el-button>
      </div>

      <!-- 课程概览 -->
      <div class="section">
        <h4>课程概览</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card shadow="hover" class="stat-card">
              <div class="stat-value">{{ report.totalCourses || 0 }}</div>
              <div class="stat-label">发布课程数</div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card shadow="hover" class="stat-card">
              <div class="stat-value">{{ report.totalStudents || 0 }}</div>
              <div class="stat-label">学习学生数</div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card shadow="hover" class="stat-card">
              <div class="stat-value">{{ report.averageScore || 0 }}</div>
              <div class="stat-label">平均评分</div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 课程参与度 -->
      <div class="section">
        <h4>课程参与度</h4>
        <div class="chart-container">
          <canvas ref="engagementChart"></canvas>
        </div>
      </div>

      <!-- 学生掌握情况 -->
      <div class="section">
        <h4>学生掌握情况</h4>
        <div class="chart-container">
          <canvas ref="masteryChart"></canvas>
        </div>
      </div>

      <!-- 课程评分分布 -->
      <div class="section">
        <h4>课程评分分布</h4>
        <div class="chart-container">
          <canvas ref="scoreChart"></canvas>
        </div>
      </div>

      <!-- 改进建议 -->
      <div class="section">
        <h4>改进建议</h4>
        <el-collapse v-model="activeNames">
          <el-collapse-item v-for="(suggestion, index) in report.suggestions" :key="index" :title="suggestion.title" :name="index">
            <div>{{ suggestion.content }}</div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>
  </div>
</template>

<script>
import Chart from 'chart.js';

export default {
  name: 'TeachingReport',
  data() {
    return {
      loading: true, // 默认显示加载状态
      report: {
        totalCourses: 0,
        totalStudents: 0,
        averageScore: 0,
        courseEngagement: [],
        studentMastery: {
          excellent: 0,
          good: 0,
          average: 0,
          needImprovement: 0
        },
        scoreDistribution: {
          "1": 0,
          "2": 0,
          "3": 0,
          "4": 0,
          "5": 0
        },
        suggestions: []
      },
      activeNames: [0],
      charts: {
        engagement: null,
        mastery: null,
        score: null
      }
    }
  },
  methods: {
    fetchReport() {
      // 显示加载状态
      this.loading = true;

      console.log('开始获取教学报告数据...');

      // 获取后端数据
      this.$axios.get('data_analysis/report/teaching', {
        // 添加超时设置
        timeout: 10000,
        // 添加错误重试
        retry: 3,
        retryDelay: 1000
      })
        .then(res => {
          console.log('教学报告响应状态码:', res.status);
          console.log('教学报告响应头:', res.headers);
          console.log('教学报告响应完整数据:', JSON.stringify(res.data));

          if (res.data && res.data.code === 0) {
            let backendData = res.data.data;
            console.log('后端返回的教学报告数据类型:', typeof backendData);
            console.log('后端返回的教学报告数据是否为null:', backendData === null);
            console.log('后端返回的教学报告数据是否为undefined:', backendData === undefined);

            // 如果后端返回的是字符串，尝试解析为JSON对象
            if (typeof backendData === 'string') {
              try {
                console.log('尝试将字符串解析为JSON对象');
                backendData = JSON.parse(backendData);
                console.log('解析后的数据:', backendData);
              } catch (e) {
                console.error('解析JSON失败:', e);
                // 如果解析失败，尝试使用eval
                try {
                  console.log('尝试使用eval解析');
                  // 使用eval前确保字符串是有效的JSON格式
                  if (backendData.startsWith('{') && backendData.endsWith('}')) {
                    backendData = eval('(' + backendData + ')');
                    console.log('eval解析后的数据:', backendData);
                  }
                } catch (evalError) {
                  console.error('eval解析失败:', evalError);
                }
              }
            }

            console.log('处理后的教学报告数据详情:', JSON.stringify(backendData));

            // 检查数据是否有效
            if (backendData && typeof backendData === 'object') {
              // 更新报告数据
              this.report = {
                // 使用默认值，确保所有必要的属性都存在
                totalCourses: backendData.totalCourses !== undefined ? backendData.totalCourses : 0,
                totalStudents: backendData.totalStudents !== undefined ? backendData.totalStudents : 0,
                averageScore: backendData.averageScore !== undefined ? backendData.averageScore : 0,
                courseEngagement: Array.isArray(backendData.courseEngagement) ? backendData.courseEngagement : [],
                studentMastery: backendData.studentMastery || {
                  excellent: 0,
                  good: 0,
                  average: 0,
                  needImprovement: 0
                },
                scoreDistribution: backendData.scoreDistribution || {
                  "1": 0,
                  "2": 0,
                  "3": 0,
                  "4": 0,
                  "5": 0
                },
                suggestions: Array.isArray(backendData.suggestions) ? backendData.suggestions : []
              };

              // 确保scoreDistribution包含所有评分
              for (let i = 1; i <= 5; i++) {
                const key = i.toString();
                if (this.report.scoreDistribution[key] === undefined) {
                  this.report.scoreDistribution[key] = 0;
                }
              }

              // 确保studentMastery包含所有属性
              if (!this.report.studentMastery.excellent) this.report.studentMastery.excellent = 0;
              if (!this.report.studentMastery.good) this.report.studentMastery.good = 0;
              if (!this.report.studentMastery.average) this.report.studentMastery.average = 0;
              if (!this.report.studentMastery.needImprovement) this.report.studentMastery.needImprovement = 0;

              console.log('处理后的报告数据:', this.report);

              // 渲染图表
              this.$nextTick(() => {
                this.renderCharts();
              });

              this.$message.success('获取教学报告成功');
            } else {
              console.error('后端返回的数据无效:', backendData);
              this.$message.error('获取教学报告失败，数据格式不正确');
              this.initEmptyReport();
            }
          } else {
            console.error('获取教学报告失败:', res.data);
            this.$message.error('获取教学报告失败，请稍后再试');
            this.initEmptyReport();
          }
        })
        .catch(err => {
          console.error('获取教学报告出错:', err);
          console.error('错误详情:', err.message);
          console.error('错误堆栈:', err.stack);
          this.$message.error('获取教学报告出错，请稍后再试');
          this.initEmptyReport();
        })
        .finally(() => {
          this.loading = false;
        });
    },

    initEmptyReport() {
      // 初始化一个空的报告对象
      this.report = {
        totalCourses: 0,
        totalStudents: 0,
        averageScore: 0,
        courseEngagement: [],
        studentMastery: {
          excellent: 0,
          good: 0,
          average: 0,
          needImprovement: 0
        },
        scoreDistribution: {
          "1": 0,
          "2": 0,
          "3": 0,
          "4": 0,
          "5": 0
        },
        suggestions: []
      };

      // 渲染图表
      this.$nextTick(() => {
        this.renderCharts();
      });
    },
    renderCharts() {
      this.renderEngagementChart();
      this.renderMasteryChart();
      this.renderScoreChart();
    },
    renderEngagementChart() {
      console.log('开始渲染课程参与度图表');

      if (this.charts.engagement) {
        this.charts.engagement.destroy();
      }

      if (!this.$refs.engagementChart) {
        console.error('课程参与度canvas元素不存在');
        return;
      }

      const ctx = this.$refs.engagementChart.getContext('2d');
      const labels = this.report.courseEngagement.map(item => item.name);
      const views = this.report.courseEngagement.map(item => item.views);
      const completions = this.report.courseEngagement.map(item => item.completions);

      this.charts.engagement = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: labels,
          datasets: [
            {
              label: '浏览次数',
              data: views,
              backgroundColor: 'rgba(54, 162, 235, 0.5)',
              borderColor: 'rgba(54, 162, 235, 1)',
              borderWidth: 1
            },
            {
              label: '完成次数',
              data: completions,
              backgroundColor: 'rgba(75, 192, 192, 0.5)',
              borderColor: 'rgba(75, 192, 192, 1)',
              borderWidth: 1
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true
            }
          }
        }
      });
    },
    renderMasteryChart() {
      console.log('开始渲染学生掌握情况图表');

      if (this.charts.mastery) {
        this.charts.mastery.destroy();
      }

      if (!this.$refs.masteryChart) {
        console.error('学生掌握情况canvas元素不存在');
        return;
      }

      const ctx = this.$refs.masteryChart.getContext('2d');
      const data = this.report.studentMastery;

      this.charts.mastery = new Chart(ctx, {
        type: 'pie',
        data: {
          labels: ['优秀 (>80%)', '良好 (60-80%)', '一般 (40-60%)', '需要提高 (<40%)'],
          datasets: [{
            data: [
              data.excellent || 0,
              data.good || 0,
              data.average || 0,
              data.needImprovement || 0
            ],
            backgroundColor: [
              'rgba(75, 192, 192, 0.5)',
              'rgba(54, 162, 235, 0.5)',
              'rgba(255, 206, 86, 0.5)',
              'rgba(255, 99, 132, 0.5)'
            ],
            borderColor: [
              'rgba(75, 192, 192, 1)',
              'rgba(54, 162, 235, 1)',
              'rgba(255, 206, 86, 1)',
              'rgba(255, 99, 132, 1)'
            ],
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'right'
            },
            tooltip: {
              callbacks: {
                label: function(tooltipItem) {
                  const dataset = tooltipItem.dataset;
                  const total = dataset.data.reduce((acc, data) => acc + data, 0);
                  const currentValue = dataset.data[tooltipItem.dataIndex];
                  const percentage = parseFloat((currentValue / total * 100).toFixed(1));
                  return `${tooltipItem.label}: ${currentValue} 人 (${percentage}%)`;
                }
              }
            }
          }
        }
      });
    },
    renderScoreChart() {
      console.log('开始渲染课程评分分布图表');

      if (this.charts.score) {
        this.charts.score.destroy();
      }

      if (!this.$refs.scoreChart) {
        console.error('课程评分分布canvas元素不存在');
        return;
      }

      const ctx = this.$refs.scoreChart.getContext('2d');
      const distribution = this.report.scoreDistribution;

      // 确保有1-5的所有评分
      const fullDistribution = {
        '1': distribution['1'] || 0,
        '2': distribution['2'] || 0,
        '3': distribution['3'] || 0,
        '4': distribution['4'] || 0,
        '5': distribution['5'] || 0
      };

      const labels = Object.keys(fullDistribution).sort();
      const data = labels.map(key => fullDistribution[key]);

      this.charts.score = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: labels,
          datasets: [{
            label: '评分分布',
            data: data,
            backgroundColor: 'rgba(153, 102, 255, 0.5)',
            borderColor: 'rgba(153, 102, 255, 1)',
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true
            }
          },
          plugins: {
            tooltip: {
              callbacks: {
                label: function(tooltipItem) {
                  return `${tooltipItem.label}分: ${tooltipItem.raw} 人`;
                }
              }
            }
          }
        }
      });
    },
    refreshData() {
      // 显示加载状态
      this.loading = true;
      this.fetchReport();
    }
  },
  mounted() {
    console.log('TeachingReport组件挂载');

    // 检查是否已登录
    if (!localStorage.getItem('token_admin')) {
      console.warn('管理员未登录，无法获取数据');
      this.$message.warning('请先登录');
      this.$router.push('/auth/login');
      return;
    }

    // 直接获取数据，不使用延迟
    console.log('开始获取数据');

    // 初始化空数据
    this.initEmptyReport();

    // 显示加载状态
    this.loading = true;

    // 获取数据
    this.fetchReport();
  },
  beforeDestroy() {
    Object.values(this.charts).forEach(chart => {
      if (chart) {
        chart.destroy();
      }
    });
  }
}
</script>

<style scoped>
.teaching-report {
  padding: 20px;
}
.section {
  margin-bottom: 30px;
  position: relative;
  z-index: 2;
}
.chart-container {
  height: 300px;
  margin-top: 20px;
  position: relative;
  z-index: 1;
}
.stat-card {
  text-align: center;
  padding: 20px;
}
.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
}
.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 8px;
}
</style>
