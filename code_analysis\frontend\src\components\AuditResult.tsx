import React, { useState } from 'react';

interface AIAnalysis {
  code_quality: {
    structure: string;
    naming: string;
    complexity: string;
  };
  functionality: {
    purpose: string;
    logic_flow: string;
    edge_cases: string;
  };
  issue_confirmation: {
    [key: string]: {
      is_valid: boolean;
      evidence: string;
      improvement: string;
    };
  };
  optimization: {
    performance_bottlenecks: string;
    optimization_suggestions: string;
    resource_efficiency: string;
  };
  best_practices: {
    code_level_improvements: string;
    coding_standards: string;
    design_patterns: string;
  };
}

interface PythonStaticAnalysis {
  pyflakes: {
    tool_info: {
      name: string;
      description: string;
      category: string;
      language: string;
    };
    issues: Array<{
      type: string;
      line: number;
      description: string;
      severity: string;
      file: string;
      tool: string;
      category: string;
    }>;
    total_issues: number;
  };
  ruff: {
    tool_info: {
      name: string;
      description: string;
      category: string;
      language: string;
      supports_autofix: boolean;
    };
    issues: Array<{
      type: string;
      line: number;
      column?: number;
      description: string;
      severity: string;
      file: string;
      tool: string;
      category: string;
      code: string;
      fixable: boolean;
    }>;
    fixes: Array<{
      type: string;
      description: string;
      diff?: string;
      file: string;
      tool: string;
    }>;
    fixed_code?: string;
    total_issues: number;
  };
  pylint: {
    tool_info: {
      name: string;
      description: string;
      category: string;
      language: string;
      provides_score: boolean;
    };
    issues: Array<{
      type: string;
      line: number;
      column?: number;
      description: string;
      severity: string;
      file: string;
      tool: string;
      category: string;
      message_id: string;
      symbol: string;
      pylint_type: string;
    }>;
    score?: number;
    stats: {
      total_issues: number;
      by_severity: { [key: string]: number };
      by_type: { [key: string]: number };
      by_category: { [key: string]: number };
    };
    total_issues: number;
  };
  summary: {
    total_issues: number;
    severity_counts: { [key: string]: number };
    risk_level: string;
    tools_used: string[];
    pylint_score?: number;
    auto_fixes_available: boolean;
    recommendations: string[];
  };
}

interface AuditResultProps {
  result: {
    status: string;
    message: string;
    project_type: string;
    suspicious_files: Array<{
      file_path: string;
      issues: Array<{
        type: string;
        line: number;
        description: string;
        severity: string;
      }>;
    }>;
    python_static?: PythonStaticAnalysis | null;
    ai_verification: {
      [key: string]: {
        issues: Array<any>;
        similar_code: Array<any>;
        ai_analysis: {
          status: string;
          analysis: {
            raw_text: string;
            summary: {
              code_quality: string;
              issue_count: number;
            };
          };
        };
      };
    } | {
      issues: Array<any>;
      similar_code: Array<any>;
      ai_analysis: {
        status: string;
        analysis: {
          raw_text: string;
          summary: {
            code_quality: string;
            issue_count: number;
          };
        };
      };
    };
    summary: {
      total_files: number;
      suspicious_files: number;
      analyzed_files?: number;
      total_issues: number;
      risk_level: string;
    };
  };
}

const AuditResult: React.FC<AuditResultProps> = ({ result }) => {
  const [activeTab, setActiveTab] = useState<'pyflakes' | 'ruff' | 'pylint'>('pyflakes');

  const renderPythonStaticAnalysis = (pythonStatic: PythonStaticAnalysis) => {
    if (!pythonStatic) {
      return null;
    }

    const renderPyflakesTab = () => (
      <div className="pyflakes-analysis">
        <div className="tool-header">
          <h4>{pythonStatic.pyflakes.tool_info.name} - {pythonStatic.pyflakes.tool_info.description}</h4>
          <p>发现 {pythonStatic.pyflakes.total_issues} 个基础逻辑错误</p>
        </div>
        <div className="issues-list">
          {pythonStatic.pyflakes.issues.length === 0 ? (
            <p className="no-issues">未发现基础逻辑错误</p>
          ) : (
            pythonStatic.pyflakes.issues.map((issue, index) => (
              <div key={index} className={`issue-item severity-${issue.severity}`}>
                <div className="issue-header">
                  <span className={`severity-badge ${issue.severity}`}>{issue.severity.toUpperCase()}</span>
                  <span className="issue-type">{issue.type}</span>
                  <span className="line-number">行 {issue.line}</span>
                </div>
                <p className="issue-description">{issue.description}</p>
              </div>
            ))
          )}
        </div>
      </div>
    );

    const renderRuffTab = () => (
      <div className="ruff-analysis">
        <div className="tool-header">
          <h4>{pythonStatic.ruff.tool_info.name} - {pythonStatic.ruff.tool_info.description}</h4>
          <p>发现 {pythonStatic.ruff.total_issues} 个中高级逻辑错误</p>
          {pythonStatic.ruff.fixes.length > 0 && (
            <p className="auto-fix-info">可自动修复 {pythonStatic.ruff.fixes.length} 个问题</p>
          )}
        </div>
        <div className="issues-list">
          {pythonStatic.ruff.issues.length === 0 ? (
            <p className="no-issues">未发现中高级逻辑错误</p>
          ) : (
            pythonStatic.ruff.issues.map((issue, index) => (
              <div key={index} className={`issue-item severity-${issue.severity}`}>
                <div className="issue-header">
                  <span className={`severity-badge ${issue.severity}`}>{issue.severity.toUpperCase()}</span>
                  <span className="issue-type">{issue.type}</span>
                  <span className="line-number">行 {issue.line}{issue.column ? `:${issue.column}` : ''}</span>
                  {issue.fixable && <span className="fixable-badge">可自动修复</span>}
                </div>
                <p className="issue-description">{issue.description}</p>
                <p className="issue-code">错误代码: {issue.code}</p>
              </div>
            ))
          )}
        </div>
        {pythonStatic.ruff.fixes.length > 0 && (
          <div className="fixes-section">
            <h5>自动修复建议</h5>
            {pythonStatic.ruff.fixes.map((fix, index) => (
              <div key={index} className="fix-item">
                <p className="fix-description">{fix.description}</p>
                {fix.diff && (
                  <pre className="fix-diff">{fix.diff}</pre>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    );

    const renderPylintTab = () => (
      <div className="pylint-analysis">
        <div className="tool-header">
          <h4>{pythonStatic.pylint.tool_info.name} - {pythonStatic.pylint.tool_info.description}</h4>
          <p>发现 {pythonStatic.pylint.total_issues} 个深度分析问题</p>
          {pythonStatic.pylint.score !== undefined && (
            <p className="pylint-score">代码质量评分: {pythonStatic.pylint.score.toFixed(2)}/10</p>
          )}
        </div>
        <div className="pylint-stats">
          <h5>统计信息</h5>
          <div className="stats-grid">
            <div className="stat-item">
              <span className="stat-label">严重错误:</span>
              <span className="stat-value">{pythonStatic.pylint.stats.by_severity?.high || 0}</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">中等问题:</span>
              <span className="stat-value">{pythonStatic.pylint.stats.by_severity?.medium || 0}</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">轻微问题:</span>
              <span className="stat-value">{pythonStatic.pylint.stats.by_severity?.low || 0}</span>
            </div>
          </div>
        </div>
        <div className="issues-list">
          {pythonStatic.pylint.issues.length === 0 ? (
            <p className="no-issues">未发现深度分析问题</p>
          ) : (
            pythonStatic.pylint.issues.map((issue, index) => (
              <div key={index} className={`issue-item severity-${issue.severity}`}>
                <div className="issue-header">
                  <span className={`severity-badge ${issue.severity}`}>{issue.severity.toUpperCase()}</span>
                  <span className="issue-type">{issue.pylint_type}</span>
                  <span className="line-number">行 {issue.line}{issue.column ? `:${issue.column}` : ''}</span>
                  <span className="message-id">{issue.message_id}</span>
                </div>
                <p className="issue-description">{issue.description}</p>
                <p className="issue-symbol">符号: {issue.symbol}</p>
              </div>
            ))
          )}
        </div>
      </div>
    );

    return (
      <div className="python-static-analysis">
        <h3>Python 静态分析结果</h3>
        <div className="analysis-summary">
          <p>总计发现 {pythonStatic.summary.total_issues} 个问题，风险等级: {pythonStatic.summary.risk_level.toUpperCase()}</p>
          {pythonStatic.summary.auto_fixes_available && (
            <p className="auto-fix-available">有自动修复建议可用</p>
          )}
        </div>

        <div className="analysis-tabs">
          <div className="tab-headers">
            <button
              className={`tab-header ${activeTab === 'pyflakes' ? 'active' : ''}`}
              onClick={() => setActiveTab('pyflakes')}
            >
              Pyflakes ({pythonStatic.pyflakes.total_issues})
            </button>
            <button
              className={`tab-header ${activeTab === 'ruff' ? 'active' : ''}`}
              onClick={() => setActiveTab('ruff')}
            >
              Ruff ({pythonStatic.ruff.total_issues})
            </button>
            <button
              className={`tab-header ${activeTab === 'pylint' ? 'active' : ''}`}
              onClick={() => setActiveTab('pylint')}
            >
              Pylint ({pythonStatic.pylint.total_issues})
            </button>
          </div>

          <div className="tab-content">
            {activeTab === 'pyflakes' && renderPyflakesTab()}
            {activeTab === 'ruff' && renderRuffTab()}
            {activeTab === 'pylint' && renderPylintTab()}
          </div>
        </div>

        {pythonStatic.summary.recommendations.length > 0 && (
          <div className="recommendations">
            <h4>改进建议</h4>
            <ul>
              {pythonStatic.summary.recommendations.map((rec, index) => (
                <li key={index}>{rec}</li>
              ))}
            </ul>
          </div>
        )}
      </div>
    );
  };

  const renderAIAnalysis = (aiVerification: any) => {
    if (!aiVerification) {
      console.log('No AI verification data');
      return null;
    }

    // 处理两种可能的数据格式：
    // 1. 多文件分析：aiVerification 是一个以文件路径为键的对象
    // 2. 单文件分析：aiVerification 可能直接是分析结果对象

    let fileAnalysis;

    if (Object.keys(aiVerification).length === 0) {
      console.log('Empty AI verification data');
      return null;
    } else if (aiVerification.ai_analysis) {
      // 单文件分析的情况
      fileAnalysis = aiVerification;
      console.log('Single file analysis detected in renderAIAnalysis');
    } else if (typeof aiVerification === 'object' && Object.keys(aiVerification).length > 0) {
      // 多文件分析的情况
      const firstKey = Object.keys(aiVerification)[0];
      fileAnalysis = aiVerification[firstKey];
      console.log('Multi-file analysis detected in renderAIAnalysis, using file:', firstKey);
    } else {
      console.log('Unknown AI verification format:', aiVerification);
      return null;
    }

    console.log('File analysis object:', fileAnalysis);

    // 检查是否有AI分析结果
    if (!fileAnalysis?.ai_analysis?.analysis?.raw_text) {
      console.log('No raw text in analysis, fileAnalysis:', fileAnalysis);

      // 尝试其他可能的路径
      if (fileAnalysis?.analysis?.raw_text) {
        console.log('Found raw text in alternative path');
        fileAnalysis = { ai_analysis: { analysis: fileAnalysis.analysis } };
      } else {
        return null;
      }
    }

    try {
      const analysis: AIAnalysis = JSON.parse(fileAnalysis.ai_analysis.analysis.raw_text);

      return (
        <div className="ai-analysis">
          <h3>AI 分析建议:</h3>

          {/* 代码质量评估 */}
          <div className="section">
            <h4>代码质量评估</h4>
            <ul>
              <li><strong>代码结构和组织:</strong> {analysis.code_quality.structure}</li>
              <li><strong>命名规范和一致性:</strong> {analysis.code_quality.naming}</li>
              <li><strong>代码复杂度和可维护性:</strong> {analysis.code_quality.complexity}</li>
            </ul>
          </div>

          {/* 功能分析 */}
          <div className="section">
            <h4>功能分析</h4>
            <ul>
              <li><strong>代码的主要功能和目的:</strong> {analysis.functionality.purpose}</li>
              <li><strong>关键算法和逻辑流程:</strong> {analysis.functionality.logic_flow}</li>
              <li><strong>潜在的边界情况和异常处理:</strong> {analysis.functionality.edge_cases}</li>
            </ul>
          </div>

          {/* 问题确认与改进建议 */}
          <div className="section">
            <h4>问题确认与改进建议</h4>
            {Object.entries(analysis.issue_confirmation).map(([issueType, issueData], index) => (
              <div key={index} className="issue-item">
                <h5>{issueType}</h5>
                <p><strong>是否有效:</strong> {issueData.is_valid ? '是' : '否'}</p>
                <p><strong>问题证据:</strong> {issueData.evidence}</p>
                <p><strong>改进建议:</strong> {issueData.improvement}</p>
              </div>
            ))}
          </div>

          {/* 性能与优化 */}
          <div className="section">
            <h4>性能与优化</h4>
            <ul>
              <li><strong>性能瓶颈分析:</strong> {analysis.optimization.performance_bottlenecks}</li>
              <li><strong>优化建议:</strong> {analysis.optimization.optimization_suggestions}</li>
              <li><strong>资源使用效率分析:</strong> {analysis.optimization.resource_efficiency}</li>
            </ul>
          </div>

          {/* 最佳实践 */}
          <div className="section">
            <h4>最佳实践</h4>
            <ul>
              <li><strong>代码级别的改进建议:</strong> {analysis.best_practices.code_level_improvements}</li>
              <li><strong>编码标准建议:</strong> {analysis.best_practices.coding_standards}</li>
              <li><strong>适用的设计模式建议:</strong> {analysis.best_practices.design_patterns}</li>
            </ul>
          </div>
        </div>
      );
    } catch (e) {
      console.error('解析 AI 分析结果失败:', e);

      // 尝试显示原始文本，即使JSON解析失败
      let rawText = fileAnalysis.ai_analysis.analysis.raw_text;

      // 尝试格式化显示
      try {
        // 如果是JSON字符串但格式有问题，尝试手动格式化
        if (rawText.includes('{') && rawText.includes('}')) {
          rawText = rawText.replace(/\\n/g, '\n').replace(/\\"/g, '"');
        }
      } catch (formatError) {
        console.error('格式化原始文本失败:', formatError);
      }

      return (
        <div className="ai-analysis error">
          <h3>AI 分析建议</h3>
          <div className="alert alert-warning">
            <p><strong>注意:</strong> JSON解析失败，显示原始分析结果</p>
            <p>错误信息: {(e as Error).message}</p>
          </div>
          <div className="raw-analysis">
            <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>{rawText}</pre>
          </div>
        </div>
      );
    }
  };

  // 检查是否为单文件分析
  const isSingleFileAnalysis = result.suspicious_files.length === 1 &&
                              result.suspicious_files[0].file_path.startsWith('temp.');

  console.log('Is single file analysis:', isSingleFileAnalysis);
  console.log('AI verification:', result.ai_verification);

  return (
    <div className="audit-result">
      {/* 审计摘要 */}
      <div className="audit-summary">
        <h2>代码分析摘要</h2>
        <ul>
          <li>状态: {result.status}</li>
          <li>消息: {result.message}</li>
          <li>分析文件数: {result.summary.analyzed_files || result.summary.suspicious_files}</li>
          <li>发现问题数: {result.summary.total_issues}</li>
          <li>代码质量评估: {result.summary.risk_level ? result.summary.risk_level.toUpperCase() : '未评估'}</li>
        </ul>
      </div>

      {/* 单文件分析时直接显示AI分析结果 */}
      {isSingleFileAnalysis && (
        <div className="single-file-analysis">
          <div className="analyzed-file">
            <h3>代码分析</h3>
            <div className="issues">
              {result.suspicious_files[0].issues.map((issue, i) => (
                <div key={i} className="issue">
                  <span className={`severity ${issue.severity}`}>{issue.severity.toUpperCase()}</span>
                  <p>{issue.description}</p>
                  <p>行号: {issue.line}</p>
                </div>
              ))}
            </div>
            {/* 显示Python静态分析结果 */}
            {result.python_static && (
              <div className="python-static-container">
                {renderPythonStaticAnalysis(result.python_static)}
              </div>
            )}

            {/* 显示AI分析结果 */}
            <div className="ai-analysis-container">
              <h4>AI 分析结果</h4>
              {renderAIAnalysis(result.ai_verification)}
            </div>
          </div>
        </div>
      )}

      {/* 多文件分析时显示文件列表 */}
      {!isSingleFileAnalysis && result.suspicious_files.map((file, index) => (
        <div key={index} className="analyzed-file">
          <h3>{file.file_path.split('\\').pop() || file.file_path.split('/').pop()}</h3>
          <div className="issues">
            {file.issues.map((issue, i) => (
              <div key={i} className="issue">
                <span className={`severity ${issue.severity}`}>{issue.severity.toUpperCase()}</span>
                <p>{issue.description}</p>
                <p>行号: {issue.line}</p>
              </div>
            ))}
          </div>
          {/* 显示AI分析结果 */}
          <div className="ai-analysis-container">
            <h4>AI 分析结果</h4>
            {renderAIAnalysis(result.ai_verification)}
          </div>
        </div>
      ))}
    </div>
  );
};

export default AuditResult;