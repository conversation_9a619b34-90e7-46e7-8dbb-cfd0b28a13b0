from werkzeug.security import generate_password_hash, check_password_hash

# 测试密码
password = "123456"

# 生成密码哈希（使用默认参数）
hashed_password = generate_password_hash(password)
print(f"生成的密码哈希: {hashed_password}")

# 验证密码
is_valid = check_password_hash(hashed_password, password)
print(f"验证结果: {is_valid}")

# 验证SQL脚本中的密码哈希
sql_hashed_password = "pbkdf2:sha256:260000$vGKWGDdRJIXYDGBP$d9a09f7de6c124a14168212e3ee4222d9d8e8014f7e242c0051a6c9cd1d952c9"
is_valid_sql = check_password_hash(sql_hashed_password, password)
print(f"SQL脚本中的密码哈希验证结果: {is_valid_sql}")

# 尝试使用不同的方法参数生成密码哈希
pbkdf2_hashed_password = generate_password_hash(password, method='pbkdf2:sha256', salt_length=16)
print(f"使用pbkdf2:sha256生成的密码哈希: {pbkdf2_hashed_password}")
is_valid_pbkdf2 = check_password_hash(pbkdf2_hashed_password, password)
print(f"pbkdf2:sha256验证结果: {is_valid_pbkdf2}")

# 尝试直接使用SQL脚本中的哈希值作为密码
direct_hashed_password = "pbkdf2:sha256:260000$vGKWGDdRJIXYDGBP$d9a09f7de6c124a14168212e3ee4222d9d8e8014f7e242c0051a6c9cd1d952c9"
print(f"直接使用的密码哈希: {direct_hashed_password}")
is_valid_direct = check_password_hash(direct_hashed_password, password)
print(f"直接使用的密码哈希验证结果: {is_valid_direct}")

# 尝试使用不同的密码
wrong_password = "1234567"
is_valid_wrong = check_password_hash(sql_hashed_password, wrong_password)
print(f"错误密码验证结果: {is_valid_wrong}")

# 尝试使用不同的密码格式
password_with_space = "123456 "
is_valid_space = check_password_hash(sql_hashed_password, password_with_space)
print(f"带空格密码验证结果: {is_valid_space}")

password_with_newline = "123456\n"
is_valid_newline = check_password_hash(sql_hashed_password, password_with_newline)
print(f"带换行符密码验证结果: {is_valid_newline}")

# 尝试使用不同的编码
password_utf8 = "123456".encode('utf-8')
try:
    is_valid_utf8 = check_password_hash(sql_hashed_password, password_utf8)
    print(f"UTF-8编码密码验证结果: {is_valid_utf8}")
except Exception as e:
    print(f"UTF-8编码密码验证错误: {e}")

# 尝试使用不同的哈希算法参数
for method in ['pbkdf2:sha256', 'pbkdf2:sha1', 'pbkdf2:sha512']:
    for salt_length in [8, 16, 32]:
        test_hashed_password = generate_password_hash(password, method=method, salt_length=salt_length)
        is_valid_test = check_password_hash(test_hashed_password, password)
        print(f"使用{method}, salt_length={salt_length}生成的密码哈希: {test_hashed_password}")
        print(f"验证结果: {is_valid_test}")
