<template>
  <div class="modules">
    <el-breadcrumb separator="/">
      <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>数据分析</el-breadcrumb-item>
      <el-breadcrumb-item>模块管理</el-breadcrumb-item>
    </el-breadcrumb>

    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>模块管理</span>
        <el-button style="float: right;" type="primary" size="small" @click="showAddDialog">添加模块</el-button>
      </div>

      <!-- 模块列表 -->
      <el-table :data="modules" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="value" label="模块名称"></el-table-column>
        <el-table-column label="操作" width="200">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </div>
    </el-card>

    <!-- 添加/编辑模块对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="500px">
      <el-form :model="form" :rules="rules" ref="form" label-width="100px">
        <el-form-item label="模块名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入模块名称"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'Modules',
  data() {
    return {
      loading: false,
      modules: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      dialogVisible: false,
      dialogTitle: '添加模块',
      form: {
        id: null,
        name: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入模块名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    fetchModules() {
      this.loading = true;
      console.log('获取模块列表...');
      
      this.$axios.get('api/modules').then(res => {
        console.log('模块数据响应状态:', res.status);
        console.log('模块数据响应:', res.data);
        
        if (res.data.code === 0 || res.data.code === 200) {
          this.modules = res.data.data || [];
          this.total = this.modules.length;
          console.log('获取到模块数量:', this.modules.length);
          
          // 如果没有模块，显示提示
          if (this.modules.length === 0) {
            this.$message.info('暂无模块数据');
          }
        } else {
          console.error('获取模块失败:', res.data.message);
          this.$message.error('获取模块列表失败: ' + (res.data.message || '未知错误'));
          
          // 使用模拟数据
          this.modules = [
            { id: 1, value: "Python编程" },
            { id: 2, value: "Web开发" },
            { id: 3, value: "数据库" },
            { id: 4, value: "数据结构" },
            { id: 5, value: "算法" },
            { id: 6, value: "机器学习" },
            { id: 7, value: "网络安全" },
            { id: 8, value: "Java开发" }
          ];
          this.total = this.modules.length;
          console.log('使用模拟数据显示');
        }
      }).catch(err => {
        console.error('获取模块出错:', err);
        console.error('错误详情:', err.response ? err.response.data : '无响应数据');
        this.$message.error('获取模块列表出错: ' + err.message);
        
        // 使用模拟数据
        this.modules = [
          { id: 1, value: "Python编程" },
          { id: 2, value: "Web开发" },
          { id: 3, value: "数据库" },
          { id: 4, value: "数据结构" },
          { id: 5, value: "算法" },
          { id: 6, value: "机器学习" },
          { id: 7, value: "网络安全" },
          { id: 8, value: "Java开发" }
        ];
        this.total = this.modules.length;
        console.log('使用模拟数据显示');
      }).finally(() => {
        this.loading = false;
      });
    },
    showAddDialog() {
      this.dialogTitle = '添加模块';
      this.form = {
        id: null,
        name: ''
      };
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },
    handleEdit(row) {
      this.dialogTitle = '编辑模块';
      this.form = {
        id: row.id,
        name: row.value
      };
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },
    handleDelete(row) {
      this.$confirm(`确认删除模块"${row.value}"吗？此操作可能会影响关联的知识点。`, '警告', {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        console.log(`尝试删除模块ID: ${row.id}, 名称: ${row.value}`);
        this.$message.info("正在删除模块，请稍候...");
        
        this.$axios.delete(`api/modules/${row.id}`).then(res => {
          console.log("删除模块响应:", res.data);
          if (res.data.code === 0 || res.data.code === 200) {
            this.$message.success(res.data.message || '删除成功');
            this.fetchModules();
          } else {
            console.error("删除模块失败:", res.data.message);
            this.$message.error(res.data.message || '删除失败，请查看控制台日志');
          }
        }).catch(error => {
          console.error("删除模块出错:", error);
          console.error("错误详情:", error.response ? error.response.data : '无响应数据');
          this.$message.error("删除模块失败！请查看控制台日志");
        });
      }).catch(() => {
        // 取消删除
        this.$message.info('已取消删除');
      });
    },
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          console.log('表单数据:', this.form);
          
          if (this.form.id) {
            // 编辑
            console.log(`尝试更新模块ID: ${this.form.id}`);
            this.$message.info("正在更新模块，请稍候...");
            
            // 打印完整的请求信息
            console.log(`PUT请求URL: api/modules/${this.form.id}`);
            console.log('请求头:', {
              'Content-Type': 'application/json',
              'Authorization': localStorage.getItem('token_admin') ? `Bearer ${localStorage.getItem('token_admin')}` : undefined
            });
            console.log('请求体:', JSON.stringify({ name: this.form.name }));
            
            this.$axios.put(`api/modules/${this.form.id}`, { name: this.form.name }).then(res => {
              console.log("更新模块响应:", res.data);
              if (res.data.code === 0 || res.data.code === 200) {
                this.$message.success('更新成功');
                this.dialogVisible = false;
                this.fetchModules();
              } else {
                console.error("更新模块失败:", res.data.message);
                this.$message.error(res.data.message || '更新失败，请查看控制台日志');
              }
            }).catch(error => {
              console.error("更新模块出错:", error);
              console.error("错误详情:", error.response ? error.response.data : '无响应数据');
              this.$message.error("更新模块失败！请查看控制台日志");
            });
          } else {
            // 添加
            console.log('尝试添加新模块');
            this.$message.info("正在添加模块，请稍候...");
            
            // 打印完整的请求信息
            console.log('POST请求URL: api/modules');
            console.log('请求头:', {
              'Content-Type': 'application/json',
              'Authorization': localStorage.getItem('token_admin') ? `Bearer ${localStorage.getItem('token_admin')}` : undefined
            });
            console.log('请求体:', JSON.stringify({ name: this.form.name }));
            
            this.$axios.post('api/modules', { name: this.form.name }).then(res => {
              console.log("添加模块响应:", res.data);
              if (res.data.code === 0 || res.data.code === 200) {
                this.$message.success('添加成功');
                this.dialogVisible = false;
                this.fetchModules();
              } else {
                console.error("添加模块失败:", res.data.message);
                this.$message.error(res.data.message || '添加失败，请查看控制台日志');
              }
            }).catch(error => {
              console.error("添加模块出错:", error);
              console.error("错误详情:", error.response ? error.response.data : '无响应数据');
              this.$message.error("添加模块失败！请查看控制台日志");
            });
          }
        } else {
          console.warn('表单验证失败');
          return false;
        }
      });
    },
    handleSizeChange(val) {
      this.pageSize = val;
    },
    handleCurrentChange(val) {
      this.currentPage = val;
    }
  },
  mounted() {
    console.log('Modules组件挂载');
    
    // 检查是否已登录
    if (!localStorage.getItem('token_admin')) {
      console.warn('管理员未登录，无法获取数据');
      this.$message.warning('请先登录');
      this.$router.push('/auth/login');
      return;
    }
    
    // 获取数据
    this.fetchModules();
  }
}
</script>

<style scoped>
.modules {
  padding: 20px;
}
.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
