from myapp import create_app, db
from myapp.models import Admin
from werkzeug.security import generate_password_hash

# 创建应用实例
app = create_app()

# 在应用上下文中运行
with app.app_context():
    # 更新所有管理员的密码为"123456"
    try:
        # 获取所有管理员
        admins = Admin.query.all()

        # 打印当前密码
        print("当前管理员密码:")
        for admin in admins:
            print(f"账号: {admin.account}, 密码哈希: {admin.password}")

        # 生成新的密码哈希
        new_password = "123456"
        hashed_password = generate_password_hash(new_password)

        # 更新所有管理员的密码
        for admin in admins:
            admin.password = hashed_password

        # 提交更改
        db.session.commit()

        # 打印更新后的密码
        print("\n更新后的管理员密码:")
        for admin in admins:
            print(f"账号: {admin.account}, 密码哈希: {admin.password}")

        print("\n所有管理员密码已更新为'123456'")
    except Exception as e:
        print(f"更新密码时发生错误: {e}")
        db.session.rollback()
