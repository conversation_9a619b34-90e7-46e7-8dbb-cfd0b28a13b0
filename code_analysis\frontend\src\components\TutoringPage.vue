<template>
  <div class="tutoring-container">
    <div class="tutoring-header">
      <h1>智能编程辅导</h1>
      <div v-if="topic && difficulty" class="tutoring-info">
        <span class="topic-badge">主题: {{ topic }}</span>
        <span :class="['difficulty-badge', difficulty.toLowerCase()]">
          难度: {{ difficulty }}
        </span>
      </div>
    </div>
    
    <div class="messages-container" ref="messagesContainer">
      <div 
        v-for="message in messages" 
        :key="message.id" 
        :class="['message', message.type === 'user' ? 'user-message' : 'assistant-message']"
      >
        <div class="message-avatar">
          <i :class="message.type === 'user' ? 'bi bi-person-fill' : 'bi bi-robot'"></i>
        </div>
        <div class="message-content">
          <div v-html="formatMessage(message.content)"></div>
          <div class="message-timestamp">
            {{ formatTime(message.timestamp) }}
          </div>
        </div>
      </div>
      
      <div v-if="isLoading" class="loading-indicator">
        <i class="bi bi-arrow-repeat spinner"></i>
        <span>思考中...</span>
      </div>
    </div>
    
    <form class="input-form" @submit.prevent="handleSubmit">
      <textarea
        v-model="input"
        placeholder="输入你的编程问题..."
        :disabled="isLoading"
        @keydown.enter.prevent="handleEnterKey"
      ></textarea>
      <button type="submit" :disabled="isLoading || !input.trim()">
        <i :class="isLoading ? 'bi bi-arrow-repeat spinner' : 'bi bi-send-fill'"></i>
      </button>
    </form>
    
    <div class="tutoring-footer">
      <p>提示: 你可以提问关于编程语言、算法、调试问题等任何编程相关的问题。</p>
    </div>
  </div>
</template>

<script>
import { marked } from 'marked';
import hljs from 'highlight.js';
import 'highlight.js/styles/github-dark.css';

// 配置marked使用highlight.js
marked.setOptions({
  highlight: function(code, lang) {
    if (lang && hljs.getLanguage(lang)) {
      return hljs.highlight(code, { language: lang }).value;
    }
    return hljs.highlightAuto(code).value;
  },
  breaks: true
});

export default {
  name: 'TutoringPage',
  data() {
    return {
      messages: [],
      input: '',
      isLoading: false,
      topic: '',
      difficulty: ''
    };
  },
  mounted() {
    // 添加欢迎消息
    this.messages.push({
      id: Date.now().toString(),
      type: 'assistant',
      content: '你好！我是你的编程辅导助手。有任何编程问题都可以问我，我会尽力帮助你解决问题。',
      timestamp: new Date()
    });
  },
  updated() {
    // 滚动到最新消息
    this.scrollToBottom();
  },
  methods: {
    formatMessage(content) {
      return marked(content);
    },
    formatTime(date) {
      return new Date(date).toLocaleTimeString();
    },
    scrollToBottom() {
      if (this.$refs.messagesContainer) {
        this.$refs.messagesContainer.scrollTop = this.$refs.messagesContainer.scrollHeight;
      }
    },
    handleEnterKey(e) {
      if (!e.shiftKey) {
        this.handleSubmit();
      }
    },
    async handleSubmit() {
      if (!this.input.trim()) return;

      // 添加用户消息
      const userMessage = {
        id: Date.now().toString(),
        type: 'user',
        content: this.input,
        timestamp: new Date()
      };
      this.messages.push(userMessage);
      const question = this.input;
      this.input = '';
      this.isLoading = true;

      try {
        // 调用API
        const response = await fetch('/api/tutoring/ask', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            question: question
          })
        });

        const data = await response.json();

        if (data.status === 'success') {
          // 添加助手回复
          const assistantMessage = {
            id: Date.now().toString(),
            type: 'assistant',
            content: data.answer,
            timestamp: new Date()
          };
          this.messages.push(assistantMessage);
          
          // 更新主题和难度
          if (data.understanding) {
            this.topic = data.understanding.topic;
            this.difficulty = data.understanding.difficulty;
          }
        } else {
          // 添加错误消息
          const errorMessage = {
            id: Date.now().toString(),
            type: 'assistant',
            content: `抱歉，我遇到了一些问题：${data.message || '未知错误'}`,
            timestamp: new Date()
          };
          this.messages.push(errorMessage);
        }
      } catch (error) {
        console.error('Error:', error);
        // 添加错误消息
        const errorMessage = {
          id: Date.now().toString(),
          type: 'assistant',
          content: '抱歉，发生了网络错误。请稍后再试。',
          timestamp: new Date()
        };
        this.messages.push(errorMessage);
      } finally {
        this.isLoading = false;
      }
    }
  }
};
</script>

<style>
.tutoring-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.tutoring-header {
  padding: 15px 0;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 20px;
}

.tutoring-header h1 {
  font-size: 24px;
  color: #343a40;
  margin: 0;
}

.tutoring-info {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.topic-badge, .difficulty-badge {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 14px;
  font-weight: 500;
}

.topic-badge {
  background-color: #e3f2fd;
  color: #0d47a1;
}

.difficulty-badge {
  background-color: #e8f5e9;
  color: #1b5e20;
}

.difficulty-badge.初级 {
  background-color: #e8f5e9;
  color: #1b5e20;
}

.difficulty-badge.中级 {
  background-color: #fff3e0;
  color: #e65100;
}

.difficulty-badge.高级 {
  background-color: #ffebee;
  color: #b71c1c;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.message {
  display: flex;
  margin-bottom: 15px;
  max-width: 80%;
}

.user-message {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.assistant-message {
  align-self: flex-start;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 10px;
  flex-shrink: 0;
}

.user-message .message-avatar {
  background-color: #4a6cf7;
  color: white;
}

.assistant-message .message-avatar {
  background-color: #6c757d;
  color: white;
}

.message-content {
  background-color: white;
  padding: 12px 15px;
  border-radius: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
}

.user-message .message-content {
  background-color: #4a6cf7;
  color: white;
  border-top-right-radius: 0;
}

.assistant-message .message-content {
  background-color: white;
  border-top-left-radius: 0;
}

.message-timestamp {
  font-size: 12px;
  color: #adb5bd;
  margin-top: 5px;
  text-align: right;
}

.user-message .message-timestamp {
  color: rgba(255, 255, 255, 0.7);
}

.input-form {
  display: flex;
  margin-top: 20px;
  padding: 10px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.05);
}

.input-form textarea {
  flex: 1;
  padding: 12px 15px;
  border: 1px solid #ced4da;
  border-radius: 5px;
  resize: none;
  height: 60px;
  font-family: inherit;
  font-size: 16px;
}

.input-form button {
  margin-left: 10px;
  padding: 0 20px;
  background-color: #4a6cf7;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.input-form button:hover {
  background-color: #3a5bd9;
}

.input-form button:disabled {
  background-color: #adb5bd;
  cursor: not-allowed;
}

.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  color: #6c757d;
}

.spinner {
  animation: spin 1s linear infinite;
  margin-right: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.tutoring-footer {
  margin-top: 20px;
  padding: 10px;
  border-top: 1px solid #e9ecef;
  color: #6c757d;
  font-size: 14px;
  text-align: center;
}

/* 代码块样式 */
.message-content pre {
  background-color: #282c34;
  border-radius: 5px;
  padding: 10px;
  overflow-x: auto;
  margin: 10px 0;
}

.user-message .message-content pre {
  background-color: rgba(0, 0, 0, 0.3);
}

.message-content code {
  font-family: 'Fira Code', monospace;
  font-size: 14px;
}

.user-message .message-content a {
  color: #e3f2fd;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tutoring-container {
    padding: 10px;
    height: calc(100vh - 20px);
  }
  
  .message {
    max-width: 90%;
  }
  
  .input-form textarea {
    font-size: 14px;
  }
}
</style>
