# 核心依赖
Flask==2.2.3
Flask-Cors==3.0.10
Flask-JWT-Extended==4.4.4
Flask-Migrate==4.0.4
Flask-SQLAlchemy==3.0.3
SQLAlchemy==2.0.15
Werkzeug==2.2.3
PyJWT==2.6.0
Jinja2==3.1.2
MarkupSafe==2.1.2
itsdangerous==2.1.2
click==8.1.3

# 数据库连接
mysql-connector-python==8.0.33
PyMySQL==1.0.3
cryptography==39.0.2

# 数据处理
pandas==2.0.1
numpy==1.24.3
scikit-learn==1.2.2
matplotlib==3.7.1
seaborn==0.12.2

# 密码加密
passlib==1.7.4
bcrypt==4.0.1

# 工具库
python-dateutil==2.8.2
python-dotenv==1.0.0
email-validator==2.0.0
Pillow==9.5.0
requests==2.30.0
tqdm==4.65.0

# 代码分析模块依赖
fastapi==0.95.2
uvicorn==0.22.0
python-multipart==0.0.6
aiofiles==23.1.0
httpx==0.24.1
openai==1.1.1
chromadb==0.4.6
sentence-transformers==2.2.2
langchain==0.0.267
langchain-community==0.0.10
pydantic==2.0.3
pydantic-settings==2.0.3

# 开发工具
pytest==7.3.1
black==23.3.0
flake8==6.0.0
isort==5.12.0
mypy==1.3.0
