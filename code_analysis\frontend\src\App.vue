<template>
  <div class="container">
    <div class="header">
      <h1>AI代码分析</h1>
      <div class="nav-buttons">
        <button
          :class="['nav-button', currentPage === 'syntax' ? 'active' : '']"
          @click="currentPage = 'syntax'"
        >
          <i class="bi bi-code-slash"></i> 语法检测
        </button>
        <button
          :class="['nav-button', currentPage === 'audit' ? 'active' : '']"
          @click="currentPage = 'audit'"
        >
          <i class="bi bi-shield-check"></i> 代码分析
        </button>
        <button
          :class="['nav-button', currentPage === 'tutoring' ? 'active' : '']"
          @click="currentPage = 'tutoring'"
        >
          <i class="bi bi-chat-dots"></i> 智能辅导
        </button>
      </div>
    </div>

    <!-- 语法检测页面 -->
    <div v-if="currentPage === 'syntax'">
      <div class="section-title">
        <h2>代码语法检测</h2>
        <p>上传代码文件或直接粘贴代码，检测语法错误</p>
      </div>

      <div class="syntax-check-container">
        <div class="syntax-input-section">
          <div class="upload-section">
            <h3>上传文件检测</h3>
            <input
              type="file"
              @change="handleSyntaxFileUpload"
              accept=".php,.java,.py,.js"
            >
            <button @click="checkSyntaxFile" :disabled="!syntaxFile">检测语法</button>
          </div>

          <div class="code-input-section">
            <h3>粘贴代码检测</h3>
            <div class="language-selector">
              <label for="language-select">选择语言:</label>
              <select id="language-select" v-model="selectedLanguage">
                <option value="python">Python</option>
                <option value="javascript">JavaScript</option>
                <option value="php">PHP</option>
                <option value="java">Java</option>
              </select>
            </div>
            <textarea
              v-model="codeInput"
              placeholder="在此粘贴代码..."
              rows="10"
            ></textarea>
            <button @click="checkSyntaxCode" :disabled="!codeInput">检测语法</button>
          </div>
        </div>

        <div class="syntax-results" v-if="syntaxResults">
          <h3>语法检测结果</h3>
          <div class="syntax-status" :class="syntaxResults.has_errors ? 'error' : 'success'">
            <i :class="syntaxResults.has_errors ? 'bi bi-x-circle' : 'bi bi-check-circle'"></i>
            <span>{{ syntaxResults.message }}</span>
          </div>

          <div class="error-list" v-if="syntaxResults.has_errors && syntaxResults.errors.length > 0">
            <h4>错误详情:</h4>
            <ul>
              <li v-for="(error, index) in syntaxResults.errors" :key="index" class="error-item">
                <span class="error-location">行 {{ error.line }}, 列 {{ error.column }}</span>
                <span class="error-message">{{ error.message }}</span>
                <span class="error-severity" :class="error.severity">{{ error.severity }}</span>
              </li>
            </ul>
          </div>
        </div>

        <div class="loading" v-if="syntaxLoading">
          检测中...
        </div>
      </div>
    </div>

    <!-- 代码分析页面 -->
    <div v-if="currentPage === 'audit'">
      <!-- API配置部分 -->
      <!-- <div class="config-section">
        <h2>API配置</h2>
        <div class="form-group">
          <input
            type="text"
            v-model="apiKey"
            placeholder="OpenAI API Key"
          >
          <input
            type="text"
            v-model="apiBase"
            placeholder="API Base URL（可选）"
          >
          <button @click="updateConfig">更新配置</button>
        </div>
      </div> -->

    <div class="upload-section">
      <input
        type="file"
        @change="handleFileUpload"
        accept=".php,.java,.py,.js"
      >
      <button @click="startAudit" :disabled="!selectedFile">开始分析</button>
    </div>

    <div class="results-section" v-if="auditResults">
      <h2>分析结果</h2>

      <div class="analysis-card">
        <h3>第一轮分析</h3>
        <pre>{{ auditResults.first_analysis }}</pre>
      </div>

      <div class="analysis-card">
        <h3>第二轮验证</h3>
        <pre>{{ auditResults.second_analysis }}</pre>
      </div>
    </div>

    <div class="loading" v-if="loading">
      分析中...
    </div>
    </div>

    <!-- 智能辅导页面 -->
    <TutoringPage v-if="currentPage === 'tutoring'" />
  </div>
</template>

<script>
import TutoringPage from './components/TutoringPage.vue';

export default {
  components: {
    TutoringPage
  },
  data() {
    return {
      selectedFile: null,
      auditResults: null,
      loading: false,
      apiKey: '',
      apiBase: '',
      fileLanguage: null,
      currentPage: 'syntax', // 默认显示语法检测页面

      // 语法检测相关数据
      syntaxFile: null,
      codeInput: '',
      selectedLanguage: 'python',
      syntaxResults: null,
      syntaxLoading: false
    }
  },
  methods: {
    async updateConfig() {
      try {
        const response = await fetch('http://localhost:8000/api/configure', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            api_key: this.apiKey,
            api_base: this.apiBase || undefined
          })
        });

        if (response.ok) {
          alert('API配置已更新');
        } else {
          throw new Error('配置更新失败');
        }
      } catch (error) {
        console.error('配置更新失败:', error);
        alert('配置更新失败');
      }
    },

    handleFileUpload(event) {
      const file = event.target.files[0];
      const supportedTypes = {
        '.php': 'PHP',
        '.java': 'Java',
        '.py': 'Python',
        '.js': 'JavaScript'
      };

      const fileExt = file.name.substring(file.name.lastIndexOf('.')).toLowerCase();
      if (!supportedTypes[fileExt]) {
        this.$message.error('不支持的文件类型。支持的文件类型包括: .php, .java, .py, .js');
        return;
      }

      this.selectedFile = file;
      this.fileLanguage = supportedTypes[fileExt];
    },

    // 处理语法检测文件上传
    handleSyntaxFileUpload(event) {
      const file = event.target.files[0];
      const supportedTypes = {
        '.php': 'PHP',
        '.java': 'Java',
        '.py': 'Python',
        '.js': 'JavaScript'
      };

      const fileExt = file.name.substring(file.name.lastIndexOf('.')).toLowerCase();
      if (!supportedTypes[fileExt]) {
        alert('不支持的文件类型。支持的文件类型包括: .php, .java, .py, .js');
        return;
      }

      this.syntaxFile = file;
    },

    // 通过文件检测语法
    async checkSyntaxFile() {
      if (!this.syntaxFile) return;

      this.syntaxLoading = true;
      this.syntaxResults = null;

      const formData = new FormData();
      formData.append('file', this.syntaxFile);

      try {
        const response = await fetch('http://localhost:8000/api/syntax/check/file', {
          method: 'POST',
          body: formData
        });

        if (!response.ok) {
          throw new Error('语法检测请求失败');
        }

        this.syntaxResults = await response.json();
      } catch (error) {
        console.error('语法检测失败:', error);
        alert('语法检测过程中发生错误');
      } finally {
        this.syntaxLoading = false;
      }
    },

    // 通过代码输入检测语法
    async checkSyntaxCode() {
      if (!this.codeInput) return;

      this.syntaxLoading = true;
      this.syntaxResults = null;

      try {
        const response = await fetch('http://localhost:8000/api/syntax/check', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            code: this.codeInput,
            language: this.selectedLanguage
          })
        });

        if (!response.ok) {
          throw new Error('语法检测请求失败');
        }

        this.syntaxResults = await response.json();
      } catch (error) {
        console.error('语法检测失败:', error);
        alert('语法检测过程中发生错误');
      } finally {
        this.syntaxLoading = false;
      }
    },

    async startAudit() {
      if (!this.selectedFile) return

      this.loading = true
      const formData = new FormData()
      formData.append('file', this.selectedFile)

      // 添加API配置
      if (this.apiKey) {
        formData.append('api_key', this.apiKey)
      }
      if (this.apiBase) {
        formData.append('api_base', this.apiBase)
      }

      try {
        const response = await fetch('http://localhost:8000/api/audit', {
          method: 'POST',
          body: formData
        })

        if (!response.ok) {
          throw new Error('审计请求失败')
        }

        this.auditResults = await response.json()
      } catch (error) {
        console.error('审计失败:', error)
        alert('审计过程中发生错误')
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e9ecef;
}

.nav-buttons {
  display: flex;
  gap: 10px;
}

.nav-button {
  padding: 8px 15px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 5px;
}

.nav-button:hover {
  background-color: #e9ecef;
}

.nav-button.active {
  background-color: #4a6cf7;
  color: white;
  border-color: #4a6cf7;
}

.config-section {
  margin: 20px 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.form-group {
  display: flex;
  gap: 10px;
  margin: 10px 0;
}

input[type="text"] {
  flex: 1;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.upload-section {
  margin: 20px 0;
}

.analysis-card {
  background: #f5f5f5;
  padding: 20px;
  margin: 10px 0;
  border-radius: 8px;
}

.loading {
  text-align: center;
  margin: 20px 0;
  font-size: 18px;
}

/* 语法检测页面样式 */
.section-title {
  margin-bottom: 20px;
  text-align: center;
}

.section-title h2 {
  font-size: 24px;
  margin-bottom: 5px;
}

.section-title p {
  color: #666;
}

.syntax-check-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.syntax-input-section {
  display: flex;
  gap: 20px;
}

.syntax-input-section > div {
  flex: 1;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.code-input-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.language-selector {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.language-selector select {
  padding: 5px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: monospace;
  resize: vertical;
}

.syntax-results {
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.syntax-status {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
}

.syntax-status.success {
  background-color: #d4edda;
  color: #155724;
}

.syntax-status.error {
  background-color: #f8d7da;
  color: #721c24;
}

.error-list {
  margin-top: 15px;
}

.error-item {
  padding: 10px;
  margin-bottom: 10px;
  background-color: #fff;
  border-left: 3px solid #dc3545;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.error-location {
  font-weight: bold;
  font-family: monospace;
}

.error-severity {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  text-transform: uppercase;
}

.error-severity.error {
  background-color: #dc3545;
  color: white;
}

.error-severity.warning {
  background-color: #ffc107;
  color: #212529;
}
</style>