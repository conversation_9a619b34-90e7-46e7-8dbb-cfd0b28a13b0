#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
这个文件包含各种常见的代码问题，用于测试静态分析工具
"""

import os
import sys
import json
import random
import datetime
from collections import defaultdict
import requests  # 未使用的导入 (Pyflakes会检测)
import time as t  # 导入但使用别名

# 全局变量
GLOBAL_CONSTANT = "这是一个全局常量"
global_var = 10  # 不符合命名规范 (Pylint会检测)

# 类定义缺少文档字符串 (Pylint会检测)
class ExampleClass:
    def __init__(self, name):
        self.name = name
        self._private = 100
        self.unused_attr = "未使用的属性"  # 未使用的属性 (Pylint会检测)
    
    # 方法缺少文档字符串 (Pylint会检测)
    def method_with_too_many_locals(self):
        local1 = 1
        local2 = 2
        local3 = 3
        local4 = 4
        local5 = 5
        local6 = 6  # 太多局部变量 (Pylint会检测)
        
        # 不必要的列表推导式 (Ruff会检测)
        result = [i for i in range(10)]
        
        # 使用 + 连接字符串 (<PERSON><PERSON>会检测)
        message = "Hello" + " " + "World"
        
        return result, message
    
    def unused_method(self):  # 未使用的方法 (Pylint会检测)
        pass

# 函数定义
def function_with_too_many_arguments(arg1, arg2, arg3, arg4, arg5, arg6, arg7):  # 参数过多 (Pylint会检测)
    """这个函数有太多参数"""
    result = arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7
    return result

# 复杂度过高的函数 (Pylint和Ruff都会检测)
def complex_function(n):
    """这个函数的复杂度过高"""
    result = 0
    for i in range(n):
        if i % 2 == 0:
            if i % 3 == 0:
                if i % 5 == 0:
                    result += i
                else:
                    result += i * 2
            else:
                if i % 7 == 0:
                    result += i * 3
                else:
                    result += i * 4
        else:
            if i % 3 == 0:
                if i % 5 == 0:
                    result += i * 5
                else:
                    result += i * 6
            else:
                if i % 7 == 0:
                    result += i * 7
                else:
                    result += i * 8
    return result

# 未使用的函数参数 (Pyflakes会检测)
def unused_params(used, unused1, unused2):
    """这个函数有未使用的参数"""
    return used * 2

# 变量名过短 (Pylint会检测)
def short_names():
    """这个函数使用了过短的变量名"""
    a = 10
    b = 20
    c = a + b
    return c

# 重定义内置函数 (Pyflakes和Pylint都会检测)
def redefine_builtin():
    """这个函数重定义了内置函数"""
    list = [1, 2, 3]
    dict = {"a": 1, "b": 2}
    return list, dict

# 使用已弃用的函数 (Ruff会检测)
def use_deprecated():
    """使用已弃用的函数"""
    import imp  # 已弃用的模块
    return imp.find_module

# 不安全的eval使用 (所有工具都会检测)
def unsafe_code(user_input):
    """这个函数使用了不安全的eval"""
    return eval(user_input)  # 安全风险

# 格式化字符串的不同方式 (Ruff会检测)
def string_formatting():
    """字符串格式化的不同方式"""
    name = "World"
    # 旧式字符串格式化 (Ruff会建议使用f-string)
    old_style = "Hello, %s!" % name
    # 使用format (Ruff会建议使用f-string)
    format_style = "Hello, {0}!".format(name)
    # 推荐的f-string
    f_string = f"Hello, {name}!"
    return old_style, format_style, f_string

# 未使用的变量 (Pyflakes会检测)
def unused_variables():
    """这个函数有未使用的变量"""
    used = 10
    unused = 20  # 未使用
    return used

# 主函数
def main():
    """主函数"""
    # 使用全局变量但未声明 (Pyflakes会检测)
    print(global_var)
    
    # 创建类实例
    example = ExampleClass("测试")
    result, message = example.method_with_too_many_locals()
    
    # 调用函数
    complex_result = complex_function(10)
    
    # 不必要的lambda (Ruff会检测)
    add = lambda x, y: x + y
    
    # 直接返回布尔比较 (Ruff会检测)
    def is_positive(n):
        if n > 0:
            return True
        else:
            return False
    
    # 使用with语句但未使用上下文管理器 (Pylint会检测)
    with open("example.txt", "w") as f:
        pass  # 未使用文件对象
    
    # 可能的类型错误 (Pylint会检测)
    value = "string"
    try:
        result = value + 10  # 字符串加整数
    except TypeError:
        result = 0
    
    return result

if __name__ == "__main__":
    # 使用魔术常量 (Pylint会检测)
    if t.time() > 1600000000:  # 硬编码的时间戳
        main()