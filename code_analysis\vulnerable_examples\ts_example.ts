// TypeScript 安全漏洞示例

// 不安全的类型断言
function unsafeTypeAssertion(data: any) {
    const user = data as { id: number, name: string, role: string };
    if (user.role === 'admin') {
        // 授予管理员权限
        grantAdminAccess(user.id);
    }
}

// 不安全的 API 密钥处理
const API_KEY: string = "sk_live_12345abcdef"; // 硬编码的 API 密钥

// 不安全的 JWT 验证
function verifyJwt(token: string): boolean {
    const parts = token.split('.');
    if (parts.length !== 3) {
        return false;
    }
    
    // 缺少签名验证
    return true;
}

// 不安全的 CORS 配置
function setupCors(app: any) {
    app.use((req: any, res: any, next: any) => {
        res.header("Access-Control-Allow-Origin", "*"); // 允许所有来源
        res.header("Access-Control-Allow-Headers", "*");
        res.header("Access-Control-Allow-Methods", "*");
        next();
    });
}

// 不安全的错误处理
function processUserData(userData: string) {
    try {
        const data = JSON.parse(userData);
        return data;
    } catch (error) {
        console.error(error); // 可能泄露敏感信息
        throw error;
    }
}

// 不安全的 DOM 操作
function updateUserProfile(userId: string, profileData: any) {
    const url = `/api/users/${userId}`;
    fetch(url, {
        method: 'POST',
        body: JSON.stringify(profileData),
        headers: {
            'Content-Type': 'application/json'
        }
    }).then(response => {
        document.getElementById('userProfile')!.innerHTML = 
            `Profile updated: ${JSON.stringify(profileData)}`; // 可能导致 XSS
    });
}

// 不安全的密码处理
function checkPassword(username: string, password: string): boolean {
    // 硬编码的密码
    const adminPassword = "admin123";
    
    if (username === "admin" && password === adminPassword) {
        return true;
    }
    return false;
}
