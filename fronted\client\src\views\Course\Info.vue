<template>
    <div>
        <van-nav-bar
                class="bg-light"
                title="课程详情"
                left-text="返回"
                left-arrow
                @click-left="$router.back()"
        />
        <p class="mt-2 h5 text-left pl-3 py-1">
            {{ this.course_info.name }}
        </p>
        <p class="small text-left ml-3">
            发布于:{{ this.date_time }}
            <span class="ml-3">收藏量: </span> {{ this.course_info.collect_num? this.course_info.collect_num:"没人收藏" }}
            <span class="ml-3">播放量: </span> {{ this.course_info.view_num? this.course_info.view_num:0 }}
        </p>
        <p class="d-flex justify-content-between mx-3">
            <small>综合评分： {{ this.course_info.score?this.course_info.score:"没有人打分" }}</small>

            <van-button v-if="!loved_it" type="info" size="mini" @click="loveIt">收藏</van-button>
            <van-button v-else type="info" size="mini" @click="dontloveIt">取消收藏</van-button>
        </p>

        <!-- 评分统计 -->
        <div v-if="showRatingStats" class="rating-stats mx-3 mb-2">
            <div class="d-flex justify-content-between align-items-center">
                <div class="rating-title small">评分统计</div>
                <div class="rating-count small text-muted">共 {{ratingStats.total}} 人评分</div>
            </div>
            <div v-for="(count, rating) in ratingStats.distribution" :key="rating" class="rating-bar d-flex align-items-center my-1">
                <div class="rating-label small mr-2">{{rating}}星</div>
                <div class="progress flex-grow-1" style="height: 8px;">
                    <div class="progress-bar"
                         :style="{width: (count / ratingStats.total * 100) + '%',
                                 backgroundColor: getRatingColor(rating)}">
                    </div>
                </div>
                <div class="rating-percent small ml-2">{{Math.round(count / ratingStats.total * 100)}}%</div>
            </div>
        </div>
        <hr>
<!--        课程内容-->
        <div v-if="course_info.type == 2">
            <video width="98%" :src="$axios.defaults.baseURL + 'file/get/' + course_info.content" controls @ended="recordCompletion"></video>
        </div>
        <div v-else style="width: 100%;overflow: scroll" id="course_content" v-html="this.course_info.content">
        </div>
        <div v-if="course_info.type != 2" class="mt-3">
            <van-button type="primary" size="large" @click="recordCompletion">标记为已完成</van-button>
        </div>

        <div class="mt-3 text-left ml-3 d-flex justify-content-around">
            <span class="font-weight-bolder small" > 打分：</span>
            <van-rate v-model="star" @change="starIt" />
            <span v-if="stared" class="small text-muted">(点击星星可修改评分)</span>
        </div>
        <hr>
<!--        评论区-->
        <div class="mt-3 pb-5">
            <comment
                    :comment_id="i.id"
                    :avatar="i.from_user.avatar"
                    :content="i.content"
                    :name="i.from_user.name"
                    :key="k"
                    :created_at="i.created_at"
                v-for="(i,k) in comments"
            ></comment>
        </div>
<!--        发布评论-->
        <div class="fixed-bottom border-top">
            <van-field
                    v-model="new_comment"
                    center
                    clearable
                    placeholder="评论课程"
            >
                <template #button>
                    <van-button @click="sendComment" size="small" type="primary">发送</van-button>
                </template>
            </van-field>
        </div>
    </div>
</template>

<script>
    import moment from 'moment'
    import Comment from "../../components/Comment";

    moment.locale('zh-cn')
    export default {
        name: "Info",
        components: {Comment},
        data() {
            return {
                course_id: this.$route.query.id,
                course_info: {},
                star:0,
                stared:false,
                new_comment:'',
                comments:[],
                loved_it:false,
                startTime: null,
                activityId: null,
                showRatingStats: false,
                ratingStats: {
                    total: 0,
                    distribution: {
                        '1': 0,
                        '2': 0,
                        '3': 0,
                        '4': 0,
                        '5': 0
                    }
                }
            }
        },
        computed: {
            date_time() {
                return moment(this.course_info.created_at).fromNow()
            }
        },
        methods: {
            starIt(star)
            {
                this.$axios.post("front/course/star/"+this.course_id+'/'+star).then(res=>{
                    console.log(res)
                    if (res.data.code == 200){
                        // 更新课程信息以显示新的平均评分
                        this.$axios.get("api/course/get/" + this.course_id).then(res => {
                            this.course_info = res.data.data;
                        });

                        // 重新加载评分统计
                        this.loadRatingStats();

                        if (this.stared) {
                            this.$toast.success("评分已更新");
                        } else {
                            this.$toast.success("评分成功");
                            this.stared = true;
                        }
                    } else {
                        this.$toast.fail("评分失败，请稍后重试");
                        return false;
                    }
                }).catch(err=>{
                    this.$toast.fail("评分失败，请重试");
                    console.error("评分错误:", err);
                    return false;
                })
            },
            sendComment()
            {
                if (this.new_comment == "") return this.$toast.fail("请输入评论内容呀！！！")
                this.$axios.post("front/course/comment/add",{
                    course_id: this.course_id,
                    content: this.new_comment
                }).then(res=>{
                    this.$toast.success("评论成功")
                    this.new_comment = ""
                    this.loadComments()
                })
            },
            loadComments()
            {
                //    加载评论
                this.$axios.get('/api/comment/all/'+this.course_id).then(res=>{
                    this.comments = res.data.data
                })
            },
            loveIt()
            {
                this.$axios.post("front/course/love/"+this.course_id).then(res=>{
                   if (res.data.code==200)
                   {
                       this.loved_it = true
                       this.$toast.success("已收藏")
                   }
                })
            },
            dontloveIt()
            {
                this.$axios.post("front/course/dontlove/"+this.course_id).then(res=>{
                    if (res.data.code==200)
                    {
                        this.loved_it = false
                        this.$toast.success("已取消收藏")
                    }
                })
            },
            // 记录学习开始时间
            recordLearningStart() {
                this.startTime = new Date();
                // 发送请求记录学习开始
                this.$axios.post('data_analysis/activity/record', {
                    course_id: this.course_id,
                    activity_type: 'learning',
                    start_time: this.startTime.toISOString(),
                    duration: 0
                }).then(res => {
                    if (res.data.code === 0) {
                        this.activityId = res.data.data.id;
                    }
                });
            },

            // 记录学习结束时间和时长
            recordLearningEnd() {
                if (this.startTime && this.activityId) {
                    const endTime = new Date();
                    const duration = Math.floor((endTime - this.startTime) / 1000); // 计算学习时长（秒）

                    // 发送请求更新学习记录
                    this.$axios.put(`data_analysis/activity/update/${this.activityId}`, {
                        end_time: endTime.toISOString(),
                        duration: duration
                    });

                    this.startTime = null;
                    this.activityId = null;
                }
            },

            // 记录课程完成
            recordCompletion() {
                this.$axios.post('data_analysis/activity/record', {
                    course_id: this.course_id,
                    activity_type: 'complete',
                    start_time: new Date().toISOString(),
                    duration: 0
                }).then(res => {
                    if (res.data.code === 0) {
                        this.$toast.success('恭喜您完成了本课程！');
                    }
                });
            },

            // 获取评分统计数据
            loadRatingStats() {
                this.$axios.get('front/course/star/stats/' + this.course_id).then(res => {
                    if (res.data.code == 200) {
                        this.ratingStats = res.data.data;
                        this.showRatingStats = this.ratingStats.total > 0;
                    }
                }).catch(err => {
                    console.error("获取评分统计失败:", err);
                });
            },

            // 根据评分获取颜色
            getRatingColor(rating) {
                const colors = {
                    '1': '#ff4d4f', // 红色
                    '2': '#faad14', // 橙色
                    '3': '#fadb14', // 黄色
                    '4': '#52c41a', // 浅绿色
                    '5': '#1890ff'  // 蓝色
                };
                return colors[rating] || '#1890ff';
            }
        },
        mounted() {
            this.$axios.get("api/course/get/" + this.course_id).then(res => {
                this.course_info = res.data.data
            })

        //    获取 我 对此课程的评分
            this.$axios.get('front/course/star/get/'+this.course_id).then(res=>{
               if (res.data.data){
                   this.star = res.data.data
                   this.stared = true
               }else{
                    this.star = 0
               }
            })

            this.loadComments()

        //    if i loved that course?
            this.$axios.get('front/course/loved/'+this.course_id).then(res=>{
                if (res.data.data=="true"){
                    this.loved_it = true
                }
            })

            // 加载评分统计
            this.loadRatingStats();

            // 记录学习开始
            this.recordLearningStart();
        },

        beforeDestroy() {
            // 记录学习结束
            this.recordLearningEnd();
        }

    }
</script>

<style scoped>
.rating-stats {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 10px;
    margin-top: 10px;
}

.rating-bar {
    margin: 5px 0;
}

.progress {
    background-color: #e9ecef;
    border-radius: 4px;
}

.rating-title {
    font-weight: bold;
}

.rating-label {
    width: 40px;
}

.rating-percent {
    width: 40px;
    text-align: right;
}
</style>