<template>
  <div class="course-knowledge-points">
    <el-breadcrumb separator="/">
      <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>数据分析</el-breadcrumb-item>
      <el-breadcrumb-item>课程知识点管理</el-breadcrumb-item>
    </el-breadcrumb>

    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>课程知识点管理</span>
      </div>

      <!-- 课程选择 -->
      <div class="course-selector">
        <el-select v-model="selectedCourseId" placeholder="请选择课程" style="width: 100%" @change="handleCourseChange">
          <el-option
            v-for="course in courses"
            :key="course.id"
            :label="course.name"
            :value="course.id">
          </el-option>
        </el-select>
      </div>

      <div v-if="selectedCourseId" class="mt-4">
        <div class="d-flex justify-content-between align-items-center mb-3">
          <h4>已关联知识点</h4>
          <el-button type="primary" size="small" @click="showAddKnowledgePointDialog">添加知识点</el-button>
        </div>

        <!-- 已关联知识点列表 -->
        <el-table :data="courseKnowledgePoints" style="width: 100%" v-loading="loading">
          <el-table-column prop="id" label="ID" width="80"></el-table-column>
          <el-table-column prop="name" label="知识点名称"></el-table-column>
          <el-table-column prop="description" label="描述" show-overflow-tooltip></el-table-column>
          <el-table-column label="操作" width="120">
            <template slot-scope="scope">
              <el-button size="mini" type="danger" @click="handleRemoveKnowledgePoint(scope.row)">移除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div v-else class="empty-state">
        <p>请先选择一个课程</p>
      </div>
    </el-card>

    <!-- 添加知识点对话框 -->
    <el-dialog title="添加知识点" :visible.sync="dialogVisible" width="500px">
      <el-form :model="form" ref="form" label-width="100px">
        <el-form-item label="知识点" prop="knowledge_point_id">
          <el-select v-model="form.knowledge_point_id" placeholder="请选择知识点" style="width: 100%">
            <el-option
              v-for="kp in availableKnowledgePoints"
              :key="kp.id"
              :label="kp.name"
              :value="kp.id">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="addKnowledgePointToCourse">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'CourseKnowledgePoints',
  data() {
    return {
      loading: false,
      courses: [],
      selectedCourseId: null,
      courseKnowledgePoints: [],
      allKnowledgePoints: [],
      dialogVisible: false,
      form: {
        knowledge_point_id: null
      }
    }
  },
  computed: {
    availableKnowledgePoints() {
      // 过滤掉已经关联的知识点
      const courseKpIds = this.courseKnowledgePoints.map(kp => kp.id);
      return this.allKnowledgePoints.filter(kp => !courseKpIds.includes(kp.id));
    }
  },
  methods: {
    fetchCourses() {
      this.loading = true;
      this.$axios.get('api/course/all').then(res => {
        console.log('课程数据响应:', res.data);
        if (res.data.code === 0) {
          this.courses = res.data.data;
          console.log('获取到课程数量:', this.courses.length);
        } else {
          console.error('获取课程失败:', res.data.message);
          // this.$message.error('获取课程列表失败: ' + (res.data.message || '未知错误'));
        }
      }).catch(err => {
        console.error('获取课程出错:', err);
        this.$message.error('获取课程列表出错: ' + err.message);
      }).finally(() => {
        this.loading = false;
      });
    },
    fetchAllKnowledgePoints() {
      this.$axios.get('data_analysis/knowledge_points').then(res => {
        console.log('所有知识点数据响应:', res.data);
        if (res.data.code === 0) {
          this.allKnowledgePoints = res.data.data;
          console.log('获取到所有知识点数量:', this.allKnowledgePoints.length);
        } else {
          console.error('获取所有知识点失败:', res.data.message);
          // this.$message.error('获取所有知识点列表失败: ' + (res.data.message || '未知错误'));
        }
      }).catch(err => {
        console.error('获取所有知识点出错:', err);
        this.$message.error('获取所有知识点列表出错: ' + err.message);
      });
    },
    fetchCourseKnowledgePoints() {
      if (!this.selectedCourseId) return;

      this.loading = true;
      this.$axios.get(`data_analysis/course/${this.selectedCourseId}/knowledge_points`).then(res => {
        console.log('课程知识点数据响应:', res.data);
        if (res.data.code === 0) {
          this.courseKnowledgePoints = res.data.data;
          console.log('获取到课程知识点数量:', this.courseKnowledgePoints.length);
        } else {
          console.error('获取课程知识点失败:', res.data.message);
          // this.$message.error('获取课程知识点列表失败: ' + (res.data.message || '未知错误'));
        }
      }).catch(err => {
        console.error('获取课程知识点出错:', err);
        this.$message.error('获取课程知识点列表出错: ' + err.message);
      }).finally(() => {
        this.loading = false;
      });
    },
    handleCourseChange() {
      console.log('选择课程:', this.selectedCourseId);

      // 使用模拟数据
      if (this.selectedCourseId === 1) {
        this.courseKnowledgePoints = [
          { id: 1, name: "变量与数据类型", description: "Python中的变量定义和基本数据类型" },
          { id: 2, name: "条件语句", description: "if-else条件判断和逻辑运算" },
          { id: 3, name: "循环结构", description: "for和while循环的使用方法" },
          { id: 4, name: "函数与方法", description: "函数定义、参数传递和返回值" }
        ];
      } else if (this.selectedCourseId === 2) {
        this.courseKnowledgePoints = [
          { id: 5, name: "数组", description: "数组的定义和操作" },
          { id: 6, name: "链表", description: "单链表和双链表" },
          { id: 7, name: "栈和队列", description: "栈和队列的实现和应用" }
        ];
      } else if (this.selectedCourseId === 3) {
        this.courseKnowledgePoints = [
          { id: 8, name: "SQL基础", description: "SQL查询语言基础" },
          { id: 9, name: "数据库设计", description: "数据库表结构设计和关系模型" }
        ];
      } else if (this.selectedCourseId === 4) {
        this.courseKnowledgePoints = [
          { id: 5, name: "HTML基础", description: "HTML标签和文档结构" },
          { id: 6, name: "CSS样式", description: "CSS选择器和样式属性" },
          { id: 7, name: "JavaScript", description: "JavaScript语法和DOM操作" }
        ];
      } else {
        this.courseKnowledgePoints = [];
      }

      // 尝试从后端获取数据
      this.fetchCourseKnowledgePoints();
    },
    showAddKnowledgePointDialog() {
      this.form.knowledge_point_id = null;
      this.dialogVisible = true;
    },
    addKnowledgePointToCourse() {
      if (!this.form.knowledge_point_id) {
        this.$message.warning('请选择知识点');
        return;
      }

      this.$axios.post(`data_analysis/course/${this.selectedCourseId}/knowledge_points`, {
        knowledge_point_id: this.form.knowledge_point_id
      }).then(res => {
        if (res.data.code === 0) {
          this.$message.success('添加知识点成功');
          this.dialogVisible = false;
          this.fetchCourseKnowledgePoints();
        } else {
          this.$message.error(res.data.message || '添加知识点失败');
        }
      }).catch(err => {
        console.error('添加知识点出错:', err);
        this.$message.error('添加知识点出错: ' + err.message);
      });
    },
    handleRemoveKnowledgePoint(row) {
      this.$confirm('确认从课程中移除该知识点吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$axios.delete(`data_analysis/course/${this.selectedCourseId}/knowledge_points/${row.id}`).then(res => {
          if (res.data.code === 0) {
            this.$message.success('移除知识点成功');
            this.fetchCourseKnowledgePoints();
          } else {
            this.$message.error(res.data.message || '移除知识点失败');
          }
        }).catch(err => {
          console.error('移除知识点出错:', err);
          this.$message.error('移除知识点出错: ' + err.message);
        });
      }).catch(() => {
        // 取消移除
      });
    }
  },
  mounted() {
    console.log('CourseKnowledgePoints组件挂载');

    // 检查是否已登录
    if (!localStorage.getItem('token_admin')) {
      console.warn('管理员未登录，无法获取数据');
      this.$message.warning('请先登录');
      this.$router.push('/auth/login');
      return;
    }

    // 使用模拟数据
    this.courses = [
      { id: 1, name: "Python编程基础" },
      { id: 2, name: "数据结构与算法" },
      { id: 3, name: "数据库原理与应用" },
      { id: 4, name: "Web前端开发" },
      { id: 5, name: "Java程序设计" },
      { id: 6, name: "机器学习基础" },
      { id: 7, name: "网络安全技术" },
      { id: 8, name: "Spring框架开发" }
    ];

    this.allKnowledgePoints = [
      { id: 1, name: "变量与数据类型", description: "Python中的变量定义和基本数据类型" },
      { id: 2, name: "条件语句", description: "if-else条件判断和逻辑运算" },
      { id: 3, name: "循环结构", description: "for和while循环的使用方法" },
      { id: 4, name: "函数与方法", description: "函数定义、参数传递和返回值" },
      { id: 5, name: "HTML基础", description: "HTML标签和文档结构" },
      { id: 6, name: "CSS样式", description: "CSS选择器和样式属性" },
      { id: 7, name: "JavaScript", description: "JavaScript语法和DOM操作" },
      { id: 8, name: "SQL基础", description: "SQL查询语言基础" }
    ];

    // 显示提示
    // this.$message.warning('使用模拟数据显示');

    // 尝试从后端获取数据
    this.fetchCourses();
    this.fetchAllKnowledgePoints();
  }
}
</script>

<style scoped>
.course-knowledge-points {
  padding: 20px;
}
.course-selector {
  margin-bottom: 20px;
}
.empty-state {
  text-align: center;
  padding: 40px 0;
  color: #909399;
}
.mt-4 {
  margin-top: 20px;
}
.mb-3 {
  margin-bottom: 15px;
}
.d-flex {
  display: flex;
}
.justify-content-between {
  justify-content: space-between;
}
.align-items-center {
  align-items: center;
}
</style>
