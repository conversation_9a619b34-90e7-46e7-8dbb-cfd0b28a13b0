# 安全漏洞示例代码

本目录包含了多种编程语言的安全漏洞示例代码，用于测试代码安全审计工具的检测能力。

## 文件说明

- `php_example.php`: PHP 安全漏洞示例，包含命令注入、SQL 注入、文件操作风险、反序列化漏洞和 XSS 漏洞等。
- `js_example.js`: JavaScript 安全漏洞示例，包含 DOM XSS、不安全的 eval 使用、原型污染、不安全的正则表达式等。
- `python_example.py`: Python 安全漏洞示例，包含命令注入、不安全的反序列化、SQL 注入、模板注入等。
- `ts_example.ts`: TypeScript 安全漏洞示例，包含不安全的类型断言、硬编码的 API 密钥、不安全的 JWT 验证等。
- `java_example.java`: Java 安全漏洞示例，包含 SQL 注入、命令注入、不安全的反序列化、不安全的文件操作等。

## 漏洞类型

这些示例代码涵盖了多种常见的安全漏洞类型：

1. **注入类漏洞**：
   - SQL 注入
   - 命令注入
   - 模板注入

2. **不安全的反序列化**：
   - PHP 的 unserialize
   - Python 的 pickle
   - Java 的 ObjectInputStream

3. **XSS 漏洞**：
   - 反射型 XSS
   - DOM XSS

4. **不安全的文件操作**：
   - 任意文件读取
   - 任意文件删除

5. **不安全的配置**：
   - 硬编码的密码和 API 密钥
   - 不安全的 CORS 配置

6. **其他漏洞**：
   - 不安全的错误处理
   - 原型污染
   - 不安全的正则表达式

## 注意事项

这些代码仅用于安全测试和教育目的，请勿在生产环境中使用。这些示例故意包含安全漏洞，用于演示不安全的编码实践。
