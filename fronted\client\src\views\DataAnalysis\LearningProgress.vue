<template>
  <div class="learning-progress">
    <van-nav-bar
      title="学习进度"
      left-text="返回"
      left-arrow
      @click-left="$router.back()"
    >
      <template #right>
        <van-button type="primary" size="small" icon="replay" @click="refreshData">刷新数据</van-button>
      </template>
    </van-nav-bar>

    <van-overlay :show="loading">
      <div class="loading-container">
        <van-loading type="spinner" color="#1989fa" />
        <div class="loading-text">加载中...</div>
      </div>
    </van-overlay>

    <!-- 知识点掌握情况 -->
    <div class="section">
      <h4>知识点掌握情况</h4>
      <div v-if="knowledgePoints.length === 0" class="empty-state">
        <van-empty description="暂无知识点掌握数据" />
        <p class="empty-tip">开始学习课程后，这里将显示您的知识点掌握情况</p>
      </div>
      <div v-else>
        <!-- 按掌握程度排序，从高到低 -->
        <div v-for="(point, index) in sortedKnowledgePoints" :key="index" class="knowledge-point">
          <div class="knowledge-point-name">{{ point.name }}</div>
          <van-progress
            :percentage="Math.round(point.mastery_level * 100)"
            :pivot-text="Math.round(point.mastery_level * 100) + '%'"
            :color="getProgressColor(point.mastery_level)"
          />
        </div>
      </div>
    </div>

    <!-- 学习趋势 -->
    <div class="section">
      <h4>学习趋势</h4>
      <div v-if="!trendData || trendData.length === 0" class="empty-state">
        <van-empty description="暂无学习趋势数据" />
        <p class="empty-tip">开始学习课程后，这里将显示您的学习趋势</p>
      </div>
      <div class="trend-chart">
        <canvas ref="trendChart"></canvas>
      </div>
    </div>

    <!-- 学习建议 -->
    <div class="section">
      <h4>学习建议</h4>
      <div v-if="suggestions.length === 0" class="empty-state">
        <van-empty description="暂无学习建议" />
        <p class="empty-tip">开始学习课程后，这里将显示针对您的学习建议</p>
      </div>
      <div v-else class="suggestions">
        <div v-for="(suggestion, index) in suggestions" :key="index" class="suggestion">
          <van-icon name="bulb-o" color="#f5a623" />
          <span>{{ suggestion }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Chart from 'chart.js';

export default {
  name: 'LearningProgress',
  data() {
    return {
      loading: true, // 添加加载状态
      knowledgePoints: [],
      trendData: [],
      suggestions: [],
      chart: null
    }
  },
  computed: {
    sortedKnowledgePoints() {
      // 按掌握程度排序，从高到低
      return [...this.knowledgePoints].sort((a, b) => b.mastery_level - a.mastery_level);
    }
  },
  methods: {
    getProgressColor(mastery) {
      // 根据掌握程度返回不同的颜色
      if (mastery >= 0.9) return '#07c160'; // 优秀 - 绿色
      if (mastery >= 0.7) return '#4a6cf7'; // 良好 - 蓝色
      if (mastery >= 0.5) return '#ff976a'; // 一般 - 橙色
      return '#ee0a24'; // 需要加强 - 红色
    },
    fetchKnowledgePointsMastery() {
      this.loading = true;

      console.log('开始获取知识点掌握情况数据...');

      // 确保URL路径正确
      const url = 'data_analysis/knowledge_points/mastery';
      console.log('请求知识点掌握情况URL:', this.$axios.defaults.baseURL + url);

      this.$axios.get(url, {
        // 添加超时设置
        timeout: 10000,
        // 添加错误重试
        retry: 3,
        retryDelay: 1000
      })
        .then(res => {
          console.log('知识点掌握情况响应状态码:', res.status);
          console.log('知识点掌握情况响应头:', res.headers);
          console.log('知识点掌握情况响应完整数据:', JSON.stringify(res.data));

          if (res.data && res.data.code === 0) {
            let backendData = res.data.data;
            console.log('后端返回的知识点掌握情况数据类型:', typeof backendData);
            console.log('后端返回的知识点掌握情况数据是否为null:', backendData === null);
            console.log('后端返回的知识点掌握情况数据是否为undefined:', backendData === undefined);

            // 如果后端返回的是字符串，尝试解析为JSON对象
            if (typeof backendData === 'string') {
              try {
                console.log('尝试将字符串解析为JSON对象');
                backendData = JSON.parse(backendData);
                console.log('解析后的数据:', backendData);
              } catch (e) {
                console.error('解析JSON失败:', e);
                // 如果解析失败，尝试使用eval
                try {
                  console.log('尝试使用eval解析');
                  // 使用eval前确保字符串是有效的JSON格式
                  if (backendData.startsWith('[') && backendData.endsWith(']')) {
                    backendData = eval('(' + backendData + ')');
                    console.log('eval解析后的数据:', backendData);
                  }
                } catch (evalError) {
                  console.error('eval解析失败:', evalError);
                }
              }
            }

            console.log('处理后的知识点掌握情况数据详情:', JSON.stringify(backendData));

            // 检查数据是否有效
            if (Array.isArray(backendData)) {
              // 确保每个知识点都有必要的属性
              this.knowledgePoints = backendData.map(kp => {
                return {
                  name: kp.name || '未命名知识点',
                  mastery_level: kp.mastery_level !== undefined ? kp.mastery_level : 0
                };
              });

              console.log('处理后的知识点掌握情况:', this.knowledgePoints);

              // 如果数据为空，显示提示
              if (this.knowledgePoints.length === 0) {
                console.warn('知识点掌握情况数据为空');
                this.$toast.fail('暂无知识点掌握情况数据');
              } else {
                this.$toast.success('获取知识点掌握情况成功');
              }
            } else {
              console.error('后端返回的知识点掌握情况数据格式不正确:', backendData);
              this.$toast.fail('获取知识点掌握情况失败，数据格式不正确');
              this.knowledgePoints = [];
            }
          } else {
            console.error('获取知识点掌握情况失败:', res.data);
            this.$toast.fail('获取知识点掌握情况失败');
            this.knowledgePoints = [];
          }
        })
        .catch(err => {
          console.error('获取知识点掌握情况出错:', err);
          console.error('错误详情:', err.message);
          console.error('错误堆栈:', err.stack);
          this.$toast.fail('获取知识点掌握情况失败');
          this.knowledgePoints = [];
        })
        .finally(() => {
          this.loading = false;
        });
    },
    fetchProgressTrend() {
      this.loading = true;

      console.log('开始获取学习趋势数据...');

      // 确保URL路径正确
      const url = 'data_analysis/progress/trend';
      console.log('请求学习趋势URL:', this.$axios.defaults.baseURL + url);

      this.$axios.get(url, {
        // 添加超时设置
        timeout: 10000,
        // 添加错误重试
        retry: 3,
        retryDelay: 1000
      })
        .then(res => {
          console.log('学习趋势响应状态码:', res.status);
          console.log('学习趋势响应头:', res.headers);
          console.log('学习趋势响应完整数据:', JSON.stringify(res.data));

          if (res.data && res.data.code === 0) {
            let backendData = res.data.data;
            console.log('后端返回的学习趋势数据类型:', typeof backendData);
            console.log('后端返回的学习趋势数据是否为null:', backendData === null);
            console.log('后端返回的学习趋势数据是否为undefined:', backendData === undefined);

            // 如果后端返回的是字符串，尝试解析为JSON对象
            if (typeof backendData === 'string') {
              try {
                console.log('尝试将字符串解析为JSON对象');
                backendData = JSON.parse(backendData);
                console.log('解析后的数据:', backendData);
              } catch (e) {
                console.error('解析JSON失败:', e);
                // 如果解析失败，尝试使用eval
                try {
                  console.log('尝试使用eval解析');
                  // 使用eval前确保字符串是有效的JSON格式
                  if (backendData.startsWith('[') && backendData.endsWith(']')) {
                    backendData = eval('(' + backendData + ')');
                    console.log('eval解析后的数据:', backendData);
                  }
                } catch (evalError) {
                  console.error('eval解析失败:', evalError);
                }
              }
            }

            console.log('处理后的学习趋势数据详情:', JSON.stringify(backendData));

            // 检查数据是否有效
            if (Array.isArray(backendData)) {
              // 确保每个趋势数据都有必要的属性
              this.trendData = backendData.map(item => {
                return {
                  date: item.date || '未知日期',
                  count: item.count !== undefined ? item.count : 0,
                  duration: item.duration !== undefined ? item.duration : 0
                };
              });

              console.log('处理后的学习趋势数据:', this.trendData);

              // 如果数据为空，显示提示
              if (this.trendData.length === 0) {
                console.warn('学习趋势数据为空');
                this.$toast.fail('暂无学习趋势数据');
              } else {
                // 渲染趋势图
                this.$nextTick(() => {
                  this.renderTrendChart();
                });
                this.$toast.success('获取学习趋势成功');
              }
            } else {
              console.error('后端返回的学习趋势数据格式不正确:', backendData);
              this.$toast.fail('获取学习趋势失败，数据格式不正确');
              this.trendData = [];
            }
          } else {
            console.error('获取学习趋势失败:', res.data);
            this.$toast.fail('获取学习趋势失败');
            this.trendData = [];
          }
        })
        .catch(err => {
          console.error('获取学习趋势出错:', err);
          console.error('错误详情:', err.message);
          console.error('错误堆栈:', err.stack);
          this.$toast.fail('获取学习趋势失败');
          this.trendData = [];
        })
        .finally(() => {
          this.loading = false;
        });
    },
    fetchPersonalReport() {
      this.loading = true;

      console.log('开始获取个人学习报告数据...');

      // 确保URL路径正确
      const url = 'data_analysis/report/personal';
      console.log('请求个人学习报告URL:', this.$axios.defaults.baseURL + url);

      this.$axios.get(url, {
        // 添加超时设置
        timeout: 10000,
        // 添加错误重试
        retry: 3,
        retryDelay: 1000
      })
        .then(res => {
          console.log('个人学习报告响应状态码:', res.status);
          console.log('个人学习报告响应头:', res.headers);
          console.log('个人学习报告响应完整数据:', JSON.stringify(res.data));

          if (res.data && res.data.code === 0) {
            let backendData = res.data.data;
            console.log('后端返回的个人学习报告数据类型:', typeof backendData);
            console.log('后端返回的个人学习报告数据是否为null:', backendData === null);
            console.log('后端返回的个人学习报告数据是否为undefined:', backendData === undefined);

            // 如果后端返回的是字符串，尝试解析为JSON对象
            if (typeof backendData === 'string') {
              try {
                console.log('尝试将字符串解析为JSON对象');
                backendData = JSON.parse(backendData);
                console.log('解析后的数据:', backendData);
              } catch (e) {
                console.error('解析JSON失败:', e);
                // 如果解析失败，尝试使用eval
                try {
                  console.log('尝试使用eval解析');
                  // 使用eval前确保字符串是有效的JSON格式
                  if (backendData.startsWith('{') && backendData.endsWith('}')) {
                    backendData = eval('(' + backendData + ')');
                    console.log('eval解析后的数据:', backendData);
                  }
                } catch (evalError) {
                  console.error('eval解析失败:', evalError);
                }
              }
            }

            console.log('处理后的个人学习报告数据详情:', JSON.stringify(backendData));

            // 检查数据是否有效
            if (backendData && typeof backendData === 'object') {
              // 检查suggestions字段是否存在且是数组
              if (backendData.suggestions && Array.isArray(backendData.suggestions)) {
                this.suggestions = backendData.suggestions;
                console.log('处理后的学习建议:', this.suggestions);

                // 如果数据为空，显示提示
                if (this.suggestions.length === 0) {
                  console.warn('学习建议数据为空');
                  // 添加一个默认建议
                  this.suggestions = ['开始学习课程后，这里将显示针对您的学习建议'];
                } else {
                  this.$toast.success('获取学习建议成功');
                }
              } else {
                console.warn('学习建议数据不存在或格式不正确');
                this.suggestions = ['开始学习课程后，这里将显示针对您的学习建议'];
              }
            } else {
              console.error('后端返回的个人学习报告数据格式不正确:', backendData);
              this.$toast.fail('获取学习建议失败，数据格式不正确');
              this.suggestions = ['开始学习课程后，这里将显示针对您的学习建议'];
            }
          } else {
            console.error('获取个人学习报告失败:', res.data);
            this.$toast.fail('获取学习建议失败');
            this.suggestions = ['开始学习课程后，这里将显示针对您的学习建议'];
          }
        })
        .catch(err => {
          console.error('获取个人学习报告出错:', err);
          console.error('错误详情:', err.message);
          console.error('错误堆栈:', err.stack);
          this.$toast.fail('获取学习建议失败');
          this.suggestions = ['开始学习课程后，这里将显示针对您的学习建议'];
        })
        .finally(() => {
          this.loading = false;
        });
    },
    refreshData() {
      // 显示加载状态
      this.loading = true;
      this.$toast.loading({
        message: '正在刷新数据...',
        forbidClick: true,
        duration: 0
      });

      // 清空现有数据
      this.knowledgePoints = [];
      this.trendData = [];
      this.suggestions = [];

      // 重新获取数据
      Promise.all([
        this.fetchKnowledgePointsMastery(),
        this.fetchProgressTrend(),
        this.fetchPersonalReport()
      ])
      .then(() => {
        this.$toast.success('数据刷新成功');
      })
      .catch(err => {
        console.error('刷新数据失败:', err);
        this.$toast.fail('刷新数据失败');
      })
      .finally(() => {
        this.loading = false;
      });
    },

    renderTrendChart() {
      if (this.chart) {
        this.chart.destroy();
      }

      if (!this.$refs.trendChart) {
        console.error('趋势图canvas元素不存在');
        return;
      }

      const ctx = this.$refs.trendChart.getContext('2d');

      // 确保有数据
      if (!this.trendData || this.trendData.length === 0) {
        console.warn('没有学习趋势数据');
        // 创建一个空图表
        this.chart = new Chart(ctx, {
          type: 'line',
          data: {
            labels: [],
            datasets: [{
              label: '学习时长（分钟）',
              data: [],
              borderColor: '#4a6cf7',
              backgroundColor: 'rgba(74, 108, 247, 0.1)',
              fill: true
            }]
          },
          options: {
            responsive: true,
            title: {
              display: true,
              text: '近30天学习时长趋势 (暂无数据)'
            }
          }
        });
        return;
      }

      // 只显示最近7天的数据，使图表更加清晰
      const recentData = [...this.trendData].slice(-7);
      const labels = recentData.map(item => {
        // 将日期格式化为更友好的显示方式，如 "5月1日"
        const date = new Date(item.date);
        return `${date.getMonth() + 1}月${date.getDate()}日`;
      });
      const durations = recentData.map(item => Math.round(item.duration / 60)); // 转换为分钟并四舍五入

      this.chart = new Chart(ctx, {
        type: 'line',
        data: {
          labels: labels,
          datasets: [{
            label: '学习时长（分钟）',
            data: durations,
            borderColor: '#4a6cf7',
            backgroundColor: 'rgba(74, 108, 247, 0.1)',
            fill: true,
            tension: 0.4 // 添加曲线平滑效果
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false, // 不保持宽高比，使用固定高度
          title: {
            display: true,
            text: '近7天学习时长趋势'
          },
          legend: {
            position: 'top'
          },
          tooltips: {
            callbacks: {
              label: function(tooltipItem, data) {
                return `学习时长: ${tooltipItem.yLabel} 分钟`;
              }
            }
          },
          scales: {
            xAxes: [{
              display: true,
              scaleLabel: {
                display: true,
                labelString: '日期'
              }
            }],
            yAxes: [{
              display: true,
              scaleLabel: {
                display: true,
                labelString: '学习时长（分钟）'
              },
              ticks: {
                beginAtZero: true,
                callback: function(value) {
                  return value + ' 分钟';
                }
              }
            }]
          }
        }
      });
    }
  },
  mounted() {
    console.log('LearningProgress组件挂载');

    // 检查是否已登录
    if (!localStorage.getItem('token_front')) {
      console.warn('用户未登录，无法获取数据');
      this.$toast.fail('请先登录');
      this.$router.replace('/auth/login');
      return;
    }

    // 直接获取数据，不使用延迟
    console.log('开始获取数据');

    // 初始化空数据
    this.knowledgePoints = [];
    this.trendData = [];
    this.suggestions = [];

    // 显示加载状态
    this.loading = true;

    // 获取数据
    this.fetchKnowledgePointsMastery();
    this.fetchProgressTrend();
    this.fetchPersonalReport();
  }
}
</script>

<style scoped>
.learning-progress {
  padding: 16px;
}
.section {
  margin-bottom: 24px;
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  position: relative; /* 确保每个section有自己的层叠上下文 */
  z-index: 2; /* 设置较高的z-index，确保内容不被遮挡 */
  overflow: visible; /* 确保内容可以溢出容器 */
}
.knowledge-point {
  margin-bottom: 12px;
}
.knowledge-point-name {
  margin-bottom: 4px;
  font-size: 14px;
}
.trend-chart {
  height: 200px;
  margin-bottom: 30px; /* 添加底部间距，避免遮挡下方内容 */
  position: relative; /* 确保图表在自己的层上 */
  z-index: 1; /* 设置较低的z-index */
}
.suggestions {
  margin-top: 8px;
}
.suggestion {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  font-size: 14px;
}
.suggestion .van-icon {
  margin-right: 8px;
  margin-top: 2px;
}

.empty-state {
  padding: 20px;
  text-align: center;
}

.empty-tip {
  color: #999;
  font-size: 14px;
  margin-top: 10px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.loading-text {
  margin-top: 10px;
  color: #333; /* 改为深灰色，更适合浅色背景 */
}
</style>
