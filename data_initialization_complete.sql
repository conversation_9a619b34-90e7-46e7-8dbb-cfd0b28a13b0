-- 完整数据初始化脚本
-- 根据 model.py 文件创建

-- 清空数据库中的所有数据
-- 禁用外键约束检查
SET FOREIGN_KEY_CHECKS = 0;

-- 清空表数据
TRUNCATE TABLE learning_reports;
TRUNCATE TABLE learning_progress;
TRUNCATE TABLE learning_activities;
TRUNCATE TABLE course_knowledge_points;
TRUNCATE TABLE knowledge_points;
TRUNCATE TABLE scores;
TRUNCATE TABLE comments;
TRUNCATE TABLE user_search_histories;
TRUNCATE TABLE user_module;
TRUNCATE TABLE user_course;
TRUNCATE TABLE courses;
TRUNCATE TABLE modules;
TRUNCATE TABLE admins;
TRUNCATE TABLE users;

-- 启用外键约束检查
SET FOREIGN_KEY_CHECKS = 1;

-- 添加用户数据（密码都是123456）
-- 使用 Werkzeug 的 generate_password_hash 函数生成的哈希值
INSERT INTO users (id, name, avatar, sex, age, account, password, created_at) VALUES
(1, '张三', 'avatar1.jpg', '男', 20, 'student1', 'scrypt:32768:8:1$57HfqYKRUl3rEOXt$baf0e5595333b8dc0b844d29a1b24a3714176db74fdcdf7083b8efe5c47656344c3f97e11597642bfcdadb2445660364c4f4135bd9f5c96917bc91d860ae6c0c', NOW() - INTERVAL 60 DAY),
(2, '李四', 'avatar2.jpg', '女', 22, 'student2', 'scrypt:32768:8:1$57HfqYKRUl3rEOXt$baf0e5595333b8dc0b844d29a1b24a3714176db74fdcdf7083b8efe5c47656344c3f97e11597642bfcdadb2445660364c4f4135bd9f5c96917bc91d860ae6c0c', NOW() - INTERVAL 55 DAY),
(3, '王五', 'avatar3.jpg', '男', 21, 'student3', 'scrypt:32768:8:1$57HfqYKRUl3rEOXt$baf0e5595333b8dc0b844d29a1b24a3714176db74fdcdf7083b8efe5c47656344c3f97e11597642bfcdadb2445660364c4f4135bd9f5c96917bc91d860ae6c0c', NOW() - INTERVAL 50 DAY),
(4, '赵六', 'avatar4.jpg', '女', 23, 'student4', 'scrypt:32768:8:1$57HfqYKRUl3rEOXt$baf0e5595333b8dc0b844d29a1b24a3714176db74fdcdf7083b8efe5c47656344c3f97e11597642bfcdadb2445660364c4f4135bd9f5c96917bc91d860ae6c0c', NOW() - INTERVAL 45 DAY),
(5, '钱七', 'avatar5.jpg', '男', 24, 'student5', 'scrypt:32768:8:1$57HfqYKRUl3rEOXt$baf0e5595333b8dc0b844d29a1b24a3714176db74fdcdf7083b8efe5c47656344c3f97e11597642bfcdadb2445660364c4f4135bd9f5c96917bc91d860ae6c0c', NOW() - INTERVAL 40 DAY);

-- 添加管理员数据（密码都是123456）
-- 使用 Werkzeug 的 generate_password_hash 函数生成的哈希值
INSERT INTO admins (id, name, avatar, account, password, phone_number, institution, post_num, created_at) VALUES
(1, '张教授', 'admin_avatar1.jpg', 'admin1', 'scrypt:32768:8:1$57HfqYKRUl3rEOXt$baf0e5595333b8dc0b844d29a1b24a3714176db74fdcdf7083b8efe5c47656344c3f97e11597642bfcdadb2445660364c4f4135bd9f5c96917bc91d860ae6c0c', '***********', '计算机科学学院', 5, NOW() - INTERVAL 90 DAY),
(2, '李博士', 'admin_avatar2.jpg', 'admin2', 'scrypt:32768:8:1$57HfqYKRUl3rEOXt$baf0e5595333b8dc0b844d29a1b24a3714176db74fdcdf7083b8efe5c47656344c3f97e11597642bfcdadb2445660364c4f4135bd9f5c96917bc91d860ae6c0c', '***********', '数学与统计学院', 3, NOW() - INTERVAL 85 DAY),
(3, '王讲师', 'admin_avatar3.jpg', 'admin3', 'scrypt:32768:8:1$57HfqYKRUl3rEOXt$baf0e5595333b8dc0b844d29a1b24a3714176db74fdcdf7083b8efe5c47656344c3f97e11597642bfcdadb2445660364c4f4135bd9f5c96917bc91d860ae6c0c', '***********', '软件工程学院', 4, NOW() - INTERVAL 80 DAY);

-- 添加模块数据
INSERT INTO modules (id, name) VALUES
(1, '编程基础'),
(2, '数据结构'),
(3, '算法设计'),
(4, '数据库'),
(5, '前端开发'),
(6, '后端开发'),
(7, '人工智能'),
(8, '网络安全');

-- 添加课程数据
INSERT INTO courses (id, name, admin_id, content, videos, type, module_id, cover, collect_num, view_num, score, created_at) VALUES
(1, 'Python编程基础', 1, 'Python编程基础课程，适合零基础学习。包括Python语法、数据类型、控制结构、函数等基础知识。', '["video1.mp4", "video2.mp4", "video3.mp4"]', 1, 1, 'python_cover.jpg', 120, 500, 4, NOW() - INTERVAL 80 DAY),
(2, '数据结构与算法', 1, '数据结构与算法基础课程，介绍常见数据结构和算法设计方法。', '["video4.mp4", "video5.mp4", "video6.mp4"]', 1, 2, 'ds_cover.jpg', 100, 450, 5, NOW() - INTERVAL 75 DAY),
(3, '数据库原理与应用', 2, '数据库原理与应用课程，包括关系型数据库设计、SQL语言、事务处理等内容。', '["video7.mp4", "video8.mp4", "video9.mp4"]', 2, 4, 'db_cover.jpg', 90, 400, 4, NOW() - INTERVAL 70 DAY),
(4, 'Web前端开发', 3, 'Web前端开发课程，包括HTML、CSS、JavaScript等前端技术。', '["video10.mp4", "video11.mp4", "video12.mp4"]', 2, 5, 'web_cover.jpg', 150, 600, 5, NOW() - INTERVAL 65 DAY),
(5, 'Java程序设计', 1, 'Java程序设计课程，包括Java语法、面向对象编程、集合框架等内容。', '["video13.mp4", "video14.mp4", "video15.mp4"]', 1, 1, 'java_cover.jpg', 110, 480, 4, NOW() - INTERVAL 60 DAY),
(6, '机器学习基础', 2, '机器学习基础课程，包括监督学习、无监督学习、强化学习等内容。', '["video16.mp4", "video17.mp4", "video18.mp4"]', 3, 7, 'ml_cover.jpg', 130, 520, 5, NOW() - INTERVAL 55 DAY),
(7, '网络安全技术', 3, '网络安全技术课程，包括网络攻防、加密算法、安全协议等内容。', '["video19.mp4", "video20.mp4", "video21.mp4"]', 3, 8, 'security_cover.jpg', 95, 420, 4, NOW() - INTERVAL 50 DAY),
(8, 'Spring框架开发', 1, 'Spring框架开发课程，包括Spring Core、Spring MVC、Spring Boot等内容。', '["video22.mp4", "video23.mp4", "video24.mp4"]', 2, 6, 'spring_cover.jpg', 85, 380, 4, NOW() - INTERVAL 45 DAY);

-- 关联用户和课程
INSERT INTO user_course (user_id, course_id) VALUES
(1, 1), (1, 2), (1, 4),
(2, 1), (2, 3), (2, 6),
(3, 4), (3, 5), (3, 8),
(4, 3), (4, 6), (4, 7),
(5, 2), (5, 7), (5, 8);

-- 关联用户和模块
INSERT INTO user_module (user_id, module_id) VALUES
(1, 1), (1, 2), (1, 5),
(2, 1), (2, 4), (2, 7),
(3, 5), (3, 6), (3, 1),
(4, 4), (4, 7), (4, 8),
(5, 2), (5, 8), (5, 6);

-- 添加知识点数据
INSERT INTO knowledge_points (id, name, description, module_id, created_at) VALUES
(1, '变量与数据类型', '理解变量的概念和基本数据类型', 1, NOW() - INTERVAL 80 DAY),
(2, '条件语句', '掌握if-else等条件控制结构', 1, NOW() - INTERVAL 79 DAY),
(3, '循环结构', '掌握for、while等循环控制结构', 1, NOW() - INTERVAL 78 DAY),
(4, '函数与方法', '理解函数的定义、调用和参数传递', 1, NOW() - INTERVAL 77 DAY),
(5, '数组', '掌握数组的创建、访问和常见操作', 2, NOW() - INTERVAL 76 DAY),
(6, '链表', '理解单链表、双链表的结构和操作', 2, NOW() - INTERVAL 75 DAY),
(7, '栈与队列', '掌握栈和队列的特性和实现方法', 2, NOW() - INTERVAL 74 DAY),
(8, '树结构', '理解二叉树、平衡树等树形结构', 2, NOW() - INTERVAL 73 DAY),
(9, '排序算法', '掌握常见排序算法的原理和实现', 3, NOW() - INTERVAL 72 DAY),
(10, '搜索算法', '理解二分查找、深度优先搜索等搜索算法', 3, NOW() - INTERVAL 71 DAY),
(11, '动态规划', '掌握动态规划的思想和应用场景', 3, NOW() - INTERVAL 70 DAY),
(12, 'SQL基础', '掌握SQL语言的基本语法和查询操作', 4, NOW() - INTERVAL 69 DAY),
(13, '数据库设计', '理解数据库设计原则和范式', 4, NOW() - INTERVAL 68 DAY),
(14, '事务处理', '掌握数据库事务的ACID特性和并发控制', 4, NOW() - INTERVAL 67 DAY),
(15, 'HTML/CSS', '掌握网页结构和样式的基本语法', 5, NOW() - INTERVAL 66 DAY),
(16, 'JavaScript', '理解JavaScript的基本语法和DOM操作', 5, NOW() - INTERVAL 65 DAY),
(17, '前端框架', '掌握Vue、React等前端框架的使用', 5, NOW() - INTERVAL 64 DAY),
(18, 'Web服务器', '理解Web服务器的工作原理和配置', 6, NOW() - INTERVAL 63 DAY),
(19, 'API设计', '掌握RESTful API的设计原则和实现', 6, NOW() - INTERVAL 62 DAY),
(20, '机器学习算法', '理解常见机器学习算法的原理和应用', 7, NOW() - INTERVAL 61 DAY),
(21, '深度学习', '掌握神经网络和深度学习的基本概念', 7, NOW() - INTERVAL 60 DAY),
(22, '网络攻防', '理解常见网络攻击方式和防御措施', 8, NOW() - INTERVAL 59 DAY),
(23, '加密算法', '掌握常见加密算法的原理和应用', 8, NOW() - INTERVAL 58 DAY);

-- 关联课程和知识点
INSERT INTO course_knowledge_points (course_id, knowledge_point_id) VALUES
-- Python编程基础课程关联知识点
(1, 1), (1, 2), (1, 3), (1, 4),
-- 数据结构与算法课程关联知识点
(2, 5), (2, 6), (2, 7), (2, 8), (2, 9), (2, 10),
-- 数据库原理与应用课程关联知识点
(3, 12), (3, 13), (3, 14),
-- Web前端开发课程关联知识点
(4, 15), (4, 16), (4, 17),
-- Java程序设计课程关联知识点
(5, 1), (5, 2), (5, 3), (5, 4),
-- 机器学习基础课程关联知识点
(6, 20), (6, 21),
-- 网络安全技术课程关联知识点
(7, 22), (7, 23),
-- Spring框架开发课程关联知识点
(8, 18), (8, 19);

-- 添加评分数据
INSERT INTO scores (user_id, score, course_id) VALUES
-- 用户1的评分
(1, 5, 1), -- Python编程基础
(1, 4, 2), -- 数据结构与算法
(1, 5, 4), -- Web前端开发
-- 用户2的评分
(2, 4, 1), -- Python编程基础
(2, 5, 3), -- 数据库原理与应用
(2, 4, 6), -- 机器学习基础
-- 用户3的评分
(3, 5, 4), -- Web前端开发
(3, 4, 5), -- Java程序设计
(3, 4, 8), -- Spring框架开发
-- 用户4的评分
(4, 5, 3), -- 数据库原理与应用
(4, 5, 6), -- 机器学习基础
(4, 4, 7), -- 网络安全技术
-- 用户5的评分
(5, 4, 2), -- 数据结构与算法
(5, 5, 7), -- 网络安全技术
(5, 4, 8); -- Spring框架开发

-- 添加评论数据
INSERT INTO comments (from_user_id, from_user_type, to_user_id, to_user_type, course_id, content, created_at) VALUES
-- 学生对课程的评论
(1, 1, 1, 2, 1, '这门课程非常适合初学者，讲解清晰，例子丰富，很容易理解。', NOW() - INTERVAL 50 DAY),
(2, 1, 1, 2, 1, '老师讲解很有耐心，对新手很友好，推荐给想学Python的同学。', NOW() - INTERVAL 48 DAY),
(3, 1, 3, 2, 4, '前端课程内容很全面，从基础到进阶都有涉及，实用性强。', NOW() - INTERVAL 45 DAY),
(4, 1, 2, 2, 6, '机器学习课程深入浅出，理论与实践结合得很好。', NOW() - INTERVAL 42 DAY),
(5, 1, 3, 2, 7, '网络安全课程案例丰富，学到了很多实用的安全知识。', NOW() - INTERVAL 40 DAY),

-- 老师回复学生的评论
(1, 2, 1, 1, 1, '谢谢你的肯定，希望你在Python的学习道路上越走越远！', NOW() - INTERVAL 49 DAY),
(1, 2, 2, 1, 1, '感谢支持，我会继续努力提高教学质量。', NOW() - INTERVAL 47 DAY),
(3, 2, 3, 1, 4, '谢谢反馈，前端技术日新月异，我会持续更新课程内容。', NOW() - INTERVAL 44 DAY),
(2, 2, 4, 1, 6, '很高兴你喜欢这门课程，机器学习是一个广阔的领域，希望这门课能为你打下良好基础。', NOW() - INTERVAL 41 DAY),
(3, 2, 5, 1, 7, '网络安全需要理论与实践相结合，希望你能学以致用。', NOW() - INTERVAL 39 DAY),

-- 学生之间的讨论
(1, 1, 2, 1, 1, '你在学习过程中遇到了哪些难点？我们可以一起讨论。', NOW() - INTERVAL 46 DAY),
(2, 1, 1, 1, 1, '函数部分有些难理解，特别是装饰器的概念，你有什么好的理解方法吗？', NOW() - INTERVAL 45 DAY),
(3, 1, 4, 1, 6, '你觉得机器学习课程中哪部分内容最有挑战性？', NOW() - INTERVAL 38 DAY),
(4, 1, 3, 1, 6, '我觉得神经网络的反向传播算法比较难理解，你呢？', NOW() - INTERVAL 37 DAY),
(5, 1, 4, 1, 7, '你有没有尝试过课程中提到的渗透测试工具？效果如何？', NOW() - INTERVAL 35 DAY);

-- 添加用户搜索历史
INSERT INTO user_search_histories (user_id, content, created_at) VALUES
(1, 'Python 函数', NOW() - INTERVAL 30 DAY),
(1, '数据结构 链表', NOW() - INTERVAL 28 DAY),
(1, 'JavaScript 基础', NOW() - INTERVAL 25 DAY),
(2, 'SQL 查询优化', NOW() - INTERVAL 29 DAY),
(2, '机器学习 算法', NOW() - INTERVAL 27 DAY),
(2, 'Python 数据分析', NOW() - INTERVAL 24 DAY),
(3, 'HTML5 新特性', NOW() - INTERVAL 26 DAY),
(3, 'CSS Flexbox', NOW() - INTERVAL 23 DAY),
(3, 'Spring Boot 入门', NOW() - INTERVAL 20 DAY),
(4, '数据库 事务', NOW() - INTERVAL 22 DAY),
(4, '深度学习 框架', NOW() - INTERVAL 19 DAY),
(4, '网络安全 加密', NOW() - INTERVAL 17 DAY),
(5, '数据结构 树', NOW() - INTERVAL 21 DAY),
(5, '网络安全 防火墙', NOW() - INTERVAL 18 DAY),
(5, 'Spring Cloud 微服务', NOW() - INTERVAL 16 DAY);

-- 添加学习活动记录
INSERT INTO learning_activities (user_id, course_id, activity_type, start_time, end_time, duration, created_at) VALUES
-- 用户1的学习活动
(1, 1, 'view', NOW() - INTERVAL 40 DAY, NOW() - INTERVAL 40 DAY + INTERVAL 30 MINUTE, 1800, NOW() - INTERVAL 40 DAY),
(1, 1, 'learning', NOW() - INTERVAL 39 DAY, NOW() - INTERVAL 39 DAY + INTERVAL 45 MINUTE, 2700, NOW() - INTERVAL 39 DAY),
(1, 1, 'learning', NOW() - INTERVAL 38 DAY, NOW() - INTERVAL 38 DAY + INTERVAL 60 MINUTE, 3600, NOW() - INTERVAL 38 DAY),
(1, 1, 'complete', NOW() - INTERVAL 37 DAY, NOW() - INTERVAL 37 DAY, 0, NOW() - INTERVAL 37 DAY),
(1, 2, 'view', NOW() - INTERVAL 36 DAY, NOW() - INTERVAL 36 DAY + INTERVAL 25 MINUTE, 1500, NOW() - INTERVAL 36 DAY),
(1, 2, 'learning', NOW() - INTERVAL 35 DAY, NOW() - INTERVAL 35 DAY + INTERVAL 40 MINUTE, 2400, NOW() - INTERVAL 35 DAY),
(1, 4, 'view', NOW() - INTERVAL 34 DAY, NOW() - INTERVAL 34 DAY + INTERVAL 20 MINUTE, 1200, NOW() - INTERVAL 34 DAY),
(1, 4, 'learning', NOW() - INTERVAL 33 DAY, NOW() - INTERVAL 33 DAY + INTERVAL 50 MINUTE, 3000, NOW() - INTERVAL 33 DAY),
(1, 4, 'complete', NOW() - INTERVAL 32 DAY, NOW() - INTERVAL 32 DAY, 0, NOW() - INTERVAL 32 DAY),

-- 用户2的学习活动
(2, 1, 'view', NOW() - INTERVAL 38 DAY, NOW() - INTERVAL 38 DAY + INTERVAL 35 MINUTE, 2100, NOW() - INTERVAL 38 DAY),
(2, 1, 'learning', NOW() - INTERVAL 37 DAY, NOW() - INTERVAL 37 DAY + INTERVAL 55 MINUTE, 3300, NOW() - INTERVAL 37 DAY),
(2, 1, 'complete', NOW() - INTERVAL 36 DAY, NOW() - INTERVAL 36 DAY, 0, NOW() - INTERVAL 36 DAY),
(2, 3, 'view', NOW() - INTERVAL 35 DAY, NOW() - INTERVAL 35 DAY + INTERVAL 30 MINUTE, 1800, NOW() - INTERVAL 35 DAY),
(2, 3, 'learning', NOW() - INTERVAL 34 DAY, NOW() - INTERVAL 34 DAY + INTERVAL 45 MINUTE, 2700, NOW() - INTERVAL 34 DAY),
(2, 6, 'view', NOW() - INTERVAL 33 DAY, NOW() - INTERVAL 33 DAY + INTERVAL 25 MINUTE, 1500, NOW() - INTERVAL 33 DAY),
(2, 6, 'learning', NOW() - INTERVAL 32 DAY, NOW() - INTERVAL 32 DAY + INTERVAL 40 MINUTE, 2400, NOW() - INTERVAL 32 DAY),

-- 用户3的学习活动
(3, 4, 'view', NOW() - INTERVAL 36 DAY, NOW() - INTERVAL 36 DAY + INTERVAL 40 MINUTE, 2400, NOW() - INTERVAL 36 DAY),
(3, 4, 'learning', NOW() - INTERVAL 35 DAY, NOW() - INTERVAL 35 DAY + INTERVAL 60 MINUTE, 3600, NOW() - INTERVAL 35 DAY),
(3, 4, 'complete', NOW() - INTERVAL 34 DAY, NOW() - INTERVAL 34 DAY, 0, NOW() - INTERVAL 34 DAY),
(3, 5, 'view', NOW() - INTERVAL 33 DAY, NOW() - INTERVAL 33 DAY + INTERVAL 30 MINUTE, 1800, NOW() - INTERVAL 33 DAY),
(3, 5, 'learning', NOW() - INTERVAL 32 DAY, NOW() - INTERVAL 32 DAY + INTERVAL 50 MINUTE, 3000, NOW() - INTERVAL 32 DAY),
(3, 8, 'view', NOW() - INTERVAL 31 DAY, NOW() - INTERVAL 31 DAY + INTERVAL 25 MINUTE, 1500, NOW() - INTERVAL 31 DAY),
(3, 8, 'learning', NOW() - INTERVAL 30 DAY, NOW() - INTERVAL 30 DAY + INTERVAL 45 MINUTE, 2700, NOW() - INTERVAL 30 DAY),

-- 用户4的学习活动
(4, 3, 'view', NOW() - INTERVAL 34 DAY, NOW() - INTERVAL 34 DAY + INTERVAL 35 MINUTE, 2100, NOW() - INTERVAL 34 DAY),
(4, 3, 'learning', NOW() - INTERVAL 33 DAY, NOW() - INTERVAL 33 DAY + INTERVAL 55 MINUTE, 3300, NOW() - INTERVAL 33 DAY),
(4, 3, 'complete', NOW() - INTERVAL 32 DAY, NOW() - INTERVAL 32 DAY, 0, NOW() - INTERVAL 32 DAY),
(4, 6, 'view', NOW() - INTERVAL 31 DAY, NOW() - INTERVAL 31 DAY + INTERVAL 40 MINUTE, 2400, NOW() - INTERVAL 31 DAY),
(4, 6, 'learning', NOW() - INTERVAL 30 DAY, NOW() - INTERVAL 30 DAY + INTERVAL 60 MINUTE, 3600, NOW() - INTERVAL 30 DAY),
(4, 7, 'view', NOW() - INTERVAL 29 DAY, NOW() - INTERVAL 29 DAY + INTERVAL 30 MINUTE, 1800, NOW() - INTERVAL 29 DAY),
(4, 7, 'learning', NOW() - INTERVAL 28 DAY, NOW() - INTERVAL 28 DAY + INTERVAL 45 MINUTE, 2700, NOW() - INTERVAL 28 DAY),

-- 用户5的学习活动
(5, 2, 'view', NOW() - INTERVAL 32 DAY, NOW() - INTERVAL 32 DAY + INTERVAL 30 MINUTE, 1800, NOW() - INTERVAL 32 DAY),
(5, 2, 'learning', NOW() - INTERVAL 31 DAY, NOW() - INTERVAL 31 DAY + INTERVAL 50 MINUTE, 3000, NOW() - INTERVAL 31 DAY),
(5, 2, 'complete', NOW() - INTERVAL 30 DAY, NOW() - INTERVAL 30 DAY, 0, NOW() - INTERVAL 30 DAY),
(5, 7, 'view', NOW() - INTERVAL 29 DAY, NOW() - INTERVAL 29 DAY + INTERVAL 35 MINUTE, 2100, NOW() - INTERVAL 29 DAY),
(5, 7, 'learning', NOW() - INTERVAL 28 DAY, NOW() - INTERVAL 28 DAY + INTERVAL 55 MINUTE, 3300, NOW() - INTERVAL 28 DAY),
(5, 8, 'view', NOW() - INTERVAL 27 DAY, NOW() - INTERVAL 27 DAY + INTERVAL 25 MINUTE, 1500, NOW() - INTERVAL 27 DAY),
(5, 8, 'learning', NOW() - INTERVAL 26 DAY, NOW() - INTERVAL 26 DAY + INTERVAL 40 MINUTE, 2400, NOW() - INTERVAL 26 DAY);

-- 添加学习进度数据
INSERT INTO learning_progress (user_id, course_id, knowledge_point_id, mastery_level, last_updated) VALUES
-- 用户1的学习进度
(1, 1, 1, 0.90, NOW() - INTERVAL 37 DAY), -- Python变量与数据类型
(1, 1, 2, 0.85, NOW() - INTERVAL 37 DAY), -- Python条件语句
(1, 1, 3, 0.80, NOW() - INTERVAL 37 DAY), -- Python循环结构
(1, 1, 4, 0.75, NOW() - INTERVAL 37 DAY), -- Python函数与方法
(1, 2, 5, 0.70, NOW() - INTERVAL 35 DAY), -- 数据结构数组
(1, 2, 6, 0.65, NOW() - INTERVAL 35 DAY), -- 数据结构链表
(1, 4, 15, 0.95, NOW() - INTERVAL 32 DAY), -- Web前端HTML/CSS
(1, 4, 16, 0.90, NOW() - INTERVAL 32 DAY), -- Web前端JavaScript
(1, 4, 17, 0.85, NOW() - INTERVAL 32 DAY), -- Web前端框架

-- 用户2的学习进度
(2, 1, 1, 0.95, NOW() - INTERVAL 36 DAY), -- Python变量与数据类型
(2, 1, 2, 0.90, NOW() - INTERVAL 36 DAY), -- Python条件语句
(2, 1, 3, 0.85, NOW() - INTERVAL 36 DAY), -- Python循环结构
(2, 1, 4, 0.80, NOW() - INTERVAL 36 DAY), -- Python函数与方法
(2, 3, 12, 0.75, NOW() - INTERVAL 34 DAY), -- 数据库SQL基础
(2, 3, 13, 0.70, NOW() - INTERVAL 34 DAY), -- 数据库设计
(2, 6, 20, 0.65, NOW() - INTERVAL 32 DAY), -- 机器学习算法
(2, 6, 21, 0.60, NOW() - INTERVAL 32 DAY), -- 深度学习

-- 用户3的学习进度
(3, 4, 15, 1.00, NOW() - INTERVAL 34 DAY), -- Web前端HTML/CSS
(3, 4, 16, 0.95, NOW() - INTERVAL 34 DAY), -- Web前端JavaScript
(3, 4, 17, 0.90, NOW() - INTERVAL 34 DAY), -- Web前端框架
(3, 5, 1, 0.85, NOW() - INTERVAL 32 DAY), -- Java变量与数据类型
(3, 5, 2, 0.80, NOW() - INTERVAL 32 DAY), -- Java条件语句
(3, 8, 18, 0.75, NOW() - INTERVAL 30 DAY), -- Spring Web服务器
(3, 8, 19, 0.70, NOW() - INTERVAL 30 DAY), -- Spring API设计

-- 用户4的学习进度
(4, 3, 12, 0.95, NOW() - INTERVAL 32 DAY), -- 数据库SQL基础
(4, 3, 13, 0.90, NOW() - INTERVAL 32 DAY), -- 数据库设计
(4, 3, 14, 0.85, NOW() - INTERVAL 32 DAY), -- 数据库事务处理
(4, 6, 20, 0.80, NOW() - INTERVAL 30 DAY), -- 机器学习算法
(4, 6, 21, 0.75, NOW() - INTERVAL 30 DAY), -- 深度学习
(4, 7, 22, 0.70, NOW() - INTERVAL 28 DAY), -- 网络安全攻防
(4, 7, 23, 0.65, NOW() - INTERVAL 28 DAY), -- 网络安全加密算法

-- 用户5的学习进度
(5, 2, 5, 0.90, NOW() - INTERVAL 30 DAY), -- 数据结构数组
(5, 2, 6, 0.85, NOW() - INTERVAL 30 DAY), -- 数据结构链表
(5, 2, 7, 0.80, NOW() - INTERVAL 30 DAY), -- 数据结构栈与队列
(5, 2, 8, 0.75, NOW() - INTERVAL 30 DAY), -- 数据结构树
(5, 7, 22, 0.95, NOW() - INTERVAL 28 DAY), -- 网络安全攻防
(5, 7, 23, 0.90, NOW() - INTERVAL 28 DAY), -- 网络安全加密算法
(5, 8, 18, 0.70, NOW() - INTERVAL 26 DAY), -- Spring Web服务器
(5, 8, 19, 0.65, NOW() - INTERVAL 26 DAY); -- Spring API设计

-- 添加学习报告数据
INSERT INTO learning_reports (user_id, report_type, content, created_at) VALUES
-- 用户1的个人学习报告
(1, 'personal', '{
    "totalCourses": 3,
    "totalHours": 15.5,
    "completionRate": 66.7,
    "knowledgePoints": [
        {"name": "变量与数据类型", "mastery_level": 0.90},
        {"name": "条件语句", "mastery_level": 0.85},
        {"name": "循环结构", "mastery_level": 0.80},
        {"name": "函数与方法", "mastery_level": 0.75},
        {"name": "数组", "mastery_level": 0.70},
        {"name": "链表", "mastery_level": 0.65},
        {"name": "HTML/CSS", "mastery_level": 0.95},
        {"name": "JavaScript", "mastery_level": 0.90},
        {"name": "前端框架", "mastery_level": 0.85}
    ],
    "suggestions": [
        "建议加强对数据结构中链表的学习",
        "您在前端开发方面表现出色，可以考虑进一步学习前端工程化",
        "建议尝试将Python和前端知识结合，学习全栈开发"
    ],
    "recommendedCourses": [
        {
            "id": 3,
            "name": "数据库原理与应用",
            "cover": "db_cover.jpg",
            "description": "推荐理由：扩展技术栈，学习后端数据处理"
        },
        {
            "id": 8,
            "name": "Spring框架开发",
            "cover": "spring_cover.jpg",
            "description": "推荐理由：了解主流后端框架，为全栈开发打基础"
        }
    ]
}', NOW() - INTERVAL 30 DAY),

-- 用户2的个人学习报告
(2, 'personal', '{
    "totalCourses": 3,
    "totalHours": 12.0,
    "completionRate": 33.3,
    "knowledgePoints": [
        {"name": "变量与数据类型", "mastery_level": 0.95},
        {"name": "条件语句", "mastery_level": 0.90},
        {"name": "循环结构", "mastery_level": 0.85},
        {"name": "函数与方法", "mastery_level": 0.80},
        {"name": "SQL基础", "mastery_level": 0.75},
        {"name": "数据库设计", "mastery_level": 0.70},
        {"name": "机器学习算法", "mastery_level": 0.65},
        {"name": "深度学习", "mastery_level": 0.60}
    ],
    "suggestions": [
        "您的Python基础扎实，建议进一步学习Python数据分析",
        "机器学习部分需要加强，特别是深度学习知识",
        "建议将数据库知识与机器学习结合，学习数据挖掘技术"
    ],
    "recommendedCourses": [
        {
            "id": 2,
            "name": "数据结构与算法",
            "cover": "ds_cover.jpg",
            "description": "推荐理由：提高算法能力，为机器学习打下基础"
        },
        {
            "id": 7,
            "name": "网络安全技术",
            "cover": "security_cover.jpg",
            "description": "推荐理由：了解数据安全知识，完善技术体系"
        }
    ]
}', NOW() - INTERVAL 29 DAY),

-- 用户3的个人学习报告
(3, 'personal', '{
    "totalCourses": 3,
    "totalHours": 13.6,
    "completionRate": 33.3,
    "knowledgePoints": [
        {"name": "HTML/CSS", "mastery_level": 1.00},
        {"name": "JavaScript", "mastery_level": 0.95},
        {"name": "前端框架", "mastery_level": 0.90},
        {"name": "变量与数据类型", "mastery_level": 0.85},
        {"name": "条件语句", "mastery_level": 0.80},
        {"name": "Web服务器", "mastery_level": 0.75},
        {"name": "API设计", "mastery_level": 0.70}
    ],
    "suggestions": [
        "您在前端开发方面表现出色，已经达到专业水平",
        "建议深入学习前端工程化和性能优化",
        "Java和Spring的学习进展良好，可以考虑全栈开发方向"
    ],
    "recommendedCourses": [
        {
            "id": 3,
            "name": "数据库原理与应用",
            "cover": "db_cover.jpg",
            "description": "推荐理由：学习后端数据处理，完善全栈技能"
        },
        {
            "id": 6,
            "name": "机器学习基础",
            "cover": "ml_cover.jpg",
            "description": "推荐理由：了解AI技术，拓展技术视野"
        }
    ]
}', NOW() - INTERVAL 28 DAY),

-- 用户4的个人学习报告
(4, 'personal', '{
    "totalCourses": 3,
    "totalHours": 14.0,
    "completionRate": 33.3,
    "knowledgePoints": [
        {"name": "SQL基础", "mastery_level": 0.95},
        {"name": "数据库设计", "mastery_level": 0.90},
        {"name": "事务处理", "mastery_level": 0.85},
        {"name": "机器学习算法", "mastery_level": 0.80},
        {"name": "深度学习", "mastery_level": 0.75},
        {"name": "网络攻防", "mastery_level": 0.70},
        {"name": "加密算法", "mastery_level": 0.65}
    ],
    "suggestions": [
        "您在数据库方面表现出色，可以考虑学习高级数据库优化",
        "机器学习和网络安全的结合是一个有前景的方向",
        "建议学习数据安全和隐私保护相关技术"
    ],
    "recommendedCourses": [
        {
            "id": 1,
            "name": "Python编程基础",
            "cover": "python_cover.jpg",
            "description": "推荐理由：Python在数据分析和机器学习中应用广泛"
        },
        {
            "id": 5,
            "name": "Java程序设计",
            "cover": "java_cover.jpg",
            "description": "推荐理由：Java在企业级应用中广泛使用，拓展就业方向"
        }
    ]
}', NOW() - INTERVAL 27 DAY),

-- 用户5的个人学习报告
(5, 'personal', '{
    "totalCourses": 3,
    "totalHours": 11.8,
    "completionRate": 33.3,
    "knowledgePoints": [
        {"name": "数组", "mastery_level": 0.90},
        {"name": "链表", "mastery_level": 0.85},
        {"name": "栈与队列", "mastery_level": 0.80},
        {"name": "树结构", "mastery_level": 0.75},
        {"name": "网络攻防", "mastery_level": 0.95},
        {"name": "加密算法", "mastery_level": 0.90},
        {"name": "Web服务器", "mastery_level": 0.70},
        {"name": "API设计", "mastery_level": 0.65}
    ],
    "suggestions": [
        "您在数据结构和网络安全方面表现出色",
        "Spring框架学习需要加强，建议多做实践项目",
        "可以考虑将安全知识应用到Web开发中，学习Web安全"
    ],
    "recommendedCourses": [
        {
            "id": 1,
            "name": "Python编程基础",
            "cover": "python_cover.jpg",
            "description": "推荐理由：Python在安全领域有广泛应用"
        },
        {
            "id": 4,
            "name": "Web前端开发",
            "cover": "web_cover.jpg",
            "description": "推荐理由：了解前端安全，形成全面的安全观"
        }
    ]
}', NOW() - INTERVAL 26 DAY),

-- 管理员1的教学效果评估报告
(1, 'teaching', '{
    "totalCourses": 3,
    "totalStudents": 8,
    "averageScore": 4.3,
    "courseEngagement": [
        {
            "name": "Python编程基础",
            "views": 120,
            "completions": 80,
            "averageDuration": 45
        },
        {
            "name": "数据结构与算法",
            "views": 100,
            "completions": 60,
            "averageDuration": 55
        },
        {
            "name": "Java程序设计",
            "views": 90,
            "completions": 50,
            "averageDuration": 50
        }
    ],
    "studentMastery": {
        "excellent": 3,
        "good": 4,
        "average": 1,
        "needImprovement": 0
    },
    "scoreDistribution": {
        "1": 0,
        "2": 0,
        "3": 1,
        "4": 3,
        "5": 4
    },
    "suggestions": [
        {
            "title": "增加实践案例",
            "content": "数据结构与算法课程可以增加更多实际应用案例，提高学生兴趣"
        },
        {
            "title": "优化课程难度曲线",
            "content": "Java程序设计课程中面向对象部分学生掌握较弱，建议调整教学进度"
        },
        {
            "title": "继续保持",
            "content": "Python编程基础课程反馈良好，建议保持现有教学方法并定期更新内容"
        }
    ]
}', NOW() - INTERVAL 25 DAY),

-- 管理员2的教学效果评估报告
(2, 'teaching', '{
    "totalCourses": 2,
    "totalStudents": 6,
    "averageScore": 4.5,
    "courseEngagement": [
        {
            "name": "数据库原理与应用",
            "views": 110,
            "completions": 70,
            "averageDuration": 50
        },
        {
            "name": "机器学习基础",
            "views": 130,
            "completions": 65,
            "averageDuration": 60
        }
    ],
    "studentMastery": {
        "excellent": 2,
        "good": 3,
        "average": 1,
        "needImprovement": 0
    },
    "scoreDistribution": {
        "1": 0,
        "2": 0,
        "3": 0,
        "4": 3,
        "5": 3
    },
    "suggestions": [
        {
            "title": "增加实验环节",
            "content": "数据库课程可以增加更多实验环节，提高学生动手能力"
        },
        {
            "title": "简化理论内容",
            "content": "机器学习基础课程中的数学理论部分可以适当简化，增加直观解释"
        },
        {
            "title": "开发在线练习系统",
            "content": "建议开发在线SQL练习系统，帮助学生巩固数据库知识"
        }
    ]
}', NOW() - INTERVAL 24 DAY),

-- 管理员3的教学效果评估报告
(3, 'teaching', '{
    "totalCourses": 3,
    "totalStudents": 7,
    "averageScore": 4.4,
    "courseEngagement": [
        {
            "name": "Web前端开发",
            "views": 150,
            "completions": 90,
            "averageDuration": 45
        },
        {
            "name": "网络安全技术",
            "views": 95,
            "completions": 55,
            "averageDuration": 55
        },
        {
            "name": "Spring框架开发",
            "views": 85,
            "completions": 45,
            "averageDuration": 50
        }
    ],
    "studentMastery": {
        "excellent": 2,
        "good": 4,
        "average": 1,
        "needImprovement": 0
    },
    "scoreDistribution": {
        "1": 0,
        "2": 0,
        "3": 0,
        "4": 4,
        "5": 3
    },
    "suggestions": [
        {
            "title": "更新前端课程内容",
            "content": "Web前端技术发展迅速，建议每季度更新一次课程内容"
        },
        {
            "title": "增加实战项目",
            "content": "Spring框架开发课程可以增加一个完整的项目实战，提高学生综合应用能力"
        },
        {
            "title": "开发安全实验环境",
            "content": "网络安全课程建议开发虚拟实验环境，让学生安全地进行渗透测试实践"
        }
    ]
}', NOW() - INTERVAL 23 DAY);

-- 更新课程表中的平均评分
UPDATE courses c
SET score = (
    SELECT AVG(score)
    FROM scores s
    WHERE s.course_id = c.id
);

-- 完成数据初始化
SELECT 'Data initialization completed successfully!' AS message;
