import uuid
from flask import request, jsonify, send_from_directory, Blueprint
# from config import Config
from config import APP_ENV, config 
import os

file = Blueprint("upload", __name__)


@file.route('/get/<filename>')
def get_img(filename):
    return send_from_directory(config[APP_ENV].UPLOAD_FOLDER,filename)


@file.route('/upload/',methods=['POST'])
def upload():
    image = request.files.get('file')
    if image:
        if not image.filename.endswith(tuple(['.jpg','.png','.mp4'])):
            return jsonify({'message':'error file format'}),409

        print(uuid.uuid4())

        filename = str(uuid.uuid4()).replace('-','') + '.' + image.filename.split('.')[-1]

        if not os.path.isdir(config[APP_ENV].UPLOAD_FOLDER):
            os.makedirs(config[APP_ENV].UPLOAD_FOLDER)
        image.save(os.path.join(config[APP_ENV].UPLOAD_FOLDER, filename))
        return filename
    else:
        return "nothing upload"
