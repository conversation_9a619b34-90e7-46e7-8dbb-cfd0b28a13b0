from flask import jsonify


class Resp:
    @staticmethod
    def success(message="success", data=None):
        if data is None:
            data = {}
        return jsonify({
            "code": 0,  # 使用0作为成功码，与前端保持一致
            "message": message,
            "data": data
        })

    @staticmethod
    def error(message="fail", code=500):
        return jsonify({
            "code": code,
            "message": message
        })