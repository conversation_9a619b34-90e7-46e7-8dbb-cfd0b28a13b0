<template>
    <div class="">
        <ul class="border-bottom pb-1" v-for="item in lists">
           <el-row>
               <el-col :span="18">
                   <p class="font-weight-bolder" style="font-size: 18px">
                       {{ item.name }}
                   </p>
               </el-col>
               <el-col :span="3">
                   <el-button type="primary" icon="el-icon-edit" circle size="small" @click="goEdit(item.id)"></el-button>
               </el-col>
               <el-col :span="3">
                   <el-button type="danger" icon="el-icon-delete" circle size="small" @click="deleteCourse(item.id)"></el-button>
               </el-col>
           </el-row>
        </ul>
    </div>
</template>

<script>
    export default {
        name: "List",
        data() {
            return {
                lists: []
            }
        },
        mounted() {
            this.loadList()
        },
        methods:{
            loadList(){
                this.$axios.get("admin/course/list").then(res=>{
                    this.lists = res.data.data
                })
            },
            goEdit(id){
                console.log(id)
                this.$router.push({
                    path:'/course/add',
                    query: {id:id}
                })
            },
            deleteCourse(id){
                if (!confirm("确认删除此课程？")) return

                console.log(`尝试删除课程ID: ${id}`);
                this.$message.info("正在删除课程，请稍候...");

                this.$axios.post("admin/course/delete/"+id).then(res=>{
                    console.log("删除课程响应:", res.data);
                    if (res.data.code == 200){
                        this.$message.success(res.data.message || "课程删除成功！");
                        this.loadList();
                    } else {
                        console.error("删除课程失败:", res.data.message);
                        this.$message.error(res.data.message || "删除失败，请查看控制台日志");
                    }
                }).catch(error=>{
                    console.error("删除课程出错:", error);
                    this.$message.error("课程删除失败！请查看控制台日志");
                })
            }
        }
    }
</script>

<style scoped>

</style>