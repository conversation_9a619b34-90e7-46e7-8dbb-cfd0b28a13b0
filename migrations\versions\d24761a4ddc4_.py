"""empty message

Revision ID: d24761a4ddc4
Revises: 55c8b26bbd43
Create Date: 2020-05-31 11:42:28.211676

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'd24761a4ddc4'
down_revision = '55c8b26bbd43'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_module',
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('module_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['module_id'], ['modules.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], )
    )
    op.create_foreign_key(None, 'comments', 'courses', ['course_id'], ['id'])
    op.create_foreign_key(None, 'courses', 'admins', ['admin_id'], ['id'])
    op.create_foreign_key(None, 'courses', 'modules', ['module_id'], ['id'])
    op.create_foreign_key(None, 'scores', 'users', ['user_id'], ['id'])
    op.create_foreign_key(None, 'scores', 'courses', ['course_id'], ['id'])
    op.create_foreign_key(None, 'user_course', 'users', ['user_id'], ['id'])
    op.create_foreign_key(None, 'user_course', 'courses', ['course_id'], ['id'])
    op.create_foreign_key(None, 'user_search_histories', 'users', ['user_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'user_search_histories', type_='foreignkey')
    op.drop_constraint(None, 'user_course', type_='foreignkey')
    op.drop_constraint(None, 'user_course', type_='foreignkey')
    op.drop_constraint(None, 'scores', type_='foreignkey')
    op.drop_constraint(None, 'scores', type_='foreignkey')
    op.drop_constraint(None, 'courses', type_='foreignkey')
    op.drop_constraint(None, 'courses', type_='foreignkey')
    op.drop_constraint(None, 'comments', type_='foreignkey')
    op.drop_table('user_module')
    # ### end Alembic commands ###
