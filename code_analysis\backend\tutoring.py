import logging
import json
from pathlib import Path
from typing import Dict, Any, List, Optional
from openai import AsyncOpenAI
from .config import settings

logger = logging.getLogger(__name__)

class TutoringService:
    """智能辅导服务"""
    
    def __init__(self):
        self.openai_api_key = settings.OPENAI_API_KEY
        self.api_base = settings.OPENAI_API_BASE
        self.model = settings.OPENAI_MODEL
        self.client = None
        
    async def ensure_initialized(self):
        """确保服务已初始化"""
        if not self.client:
            # 从配置文件读取API设置
            config_path = Path(__file__).parent.parent / "config" / "api_config.json"
            if config_path.exists():
                try:
                    # 使用 utf-8-sig 编码读取文件，这会自动处理 BOM
                    with open(config_path, "r", encoding='utf-8-sig') as f:
                        config = json.load(f)
                        self.openai_api_key = config.get("api_key", self.openai_api_key)
                        self.api_base = config.get("api_base", self.api_base)
                        self.model = config.get("model", self.model)
                except Exception as e:
                    logger.warning(f"读取配置文件失败，使用默认设置: {str(e)}")
            
            # 创建 OpenAI 客户端
            self.client = AsyncOpenAI(
                api_key=self.openai_api_key,
                base_url=self.api_base
            )
            logger.info("辅导服务初始化完成")
    
    async def ask_question(self, question: str, context: Optional[str] = None) -> Dict[str, Any]:
        """处理学生提问"""
        await self.ensure_initialized()
        
        try:
            # 构建提示
            system_prompt = """你是一个专业的编程辅导老师，擅长解答学生在编程学习中遇到的问题。
请提供清晰、准确、有教育意义的回答，并尽可能包含代码示例和解释。
回答应该既有理论知识，也有实践指导，帮助学生真正理解问题。"""
            
            messages = [
                {"role": "system", "content": system_prompt},
            ]
            
            # 如果有上下文，添加到提示中
            if context:
                messages.append({"role": "user", "content": f"我正在学习以下代码或概念:\n\n{context}"})
            
            # 添加学生的问题
            messages.append({"role": "user", "content": question})
            
            # 调用 API
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=0.3,
                max_tokens=2000
            )
            
            # 获取回答
            answer = response.choices[0].message.content
            
            # 构造响应
            return {
                "status": "success",
                "answer": answer,
                "understanding": {
                    "topic": self._extract_topic(question),
                    "difficulty": self._estimate_difficulty(question)
                }
            }
            
        except Exception as e:
            logger.error(f"处理问题失败: {str(e)}")
            return {
                "status": "error",
                "message": f"处理问题失败: {str(e)}",
                "answer": "抱歉，我无法处理您的问题。请稍后再试或联系管理员。"
            }
    
    def _extract_topic(self, question: str) -> str:
        """提取问题的主题"""
        # 简单实现，实际可以使用更复杂的NLP技术
        topics = {
            "python": ["python", "py", "列表", "字典", "元组", "函数", "类", "对象"],
            "javascript": ["javascript", "js", "dom", "html", "css", "前端"],
            "数据库": ["sql", "数据库", "查询", "表", "索引"],
            "算法": ["算法", "排序", "搜索", "复杂度", "数据结构"],
            "网络": ["http", "tcp", "ip", "网络", "请求", "响应"]
        }
        
        question_lower = question.lower()
        for topic, keywords in topics.items():
            if any(keyword in question_lower for keyword in keywords):
                return topic
        
        return "编程"
    
    def _estimate_difficulty(self, question: str) -> str:
        """估计问题的难度"""
        # 简单实现，实际可以使用更复杂的NLP技术
        easy_keywords = ["基础", "入门", "简单", "怎么", "是什么"]
        hard_keywords = ["高级", "复杂", "优化", "性能", "架构", "设计模式"]
        
        question_lower = question.lower()
        
        if any(keyword in question_lower for keyword in hard_keywords):
            return "高级"
        elif any(keyword in question_lower for keyword in easy_keywords):
            return "初级"
        else:
            return "中级"
