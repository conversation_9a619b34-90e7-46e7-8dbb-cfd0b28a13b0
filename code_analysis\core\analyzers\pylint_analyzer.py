import subprocess
import tempfile
import os
import json
import logging
import re
from typing import List, Dict, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class PylintAnalyzer:
    """Pylint分析器 - 深度逻辑分析"""
    
    def __init__(self):
        self.name = "Pylint"
        self.description = "深度逻辑分析"
        
    def analyze_file(self, file_path: str) -> Dict[str, Any]:
        """分析单个Python文件"""
        try:
            if not file_path.endswith('.py'):
                return {'issues': [], 'score': None, 'stats': {}}
                
            # 检查文件是否存在
            if not os.path.exists(file_path):
                logger.error(f"文件不存在: {file_path}")
                return {'issues': [], 'score': None, 'stats': {}}
                
            # 运行pylint，使用文本格式而不是JSON格式
            result = subprocess.run(
                ['pylint', '--output-format=text', file_path],
                capture_output=True,
                text=True,
                encoding='utf-8'
            )
            
            issues = []
            score = None
            
            # 直接解析文本输出
            if result.stdout:
                issues = self._parse_text_output(result.stdout, file_path)
                score = self._extract_score_from_text(result.stdout)
            
            # 生成统计信息
            stats = self._generate_stats(issues)
            
            return {
                'issues': issues,
                'score': score,
                'stats': stats
            }
            
        except FileNotFoundError:
            logger.error("Pylint未安装或不在PATH中")
            return {
                'issues': [{
                    'type': 'tool_error',
                    'line': 0,
                    'description': 'Pylint工具未安装',
                    'severity': 'error',
                    'file': file_path,
                    'tool': 'pylint'
                }],
                'score': None,
                'stats': {}
            }
        except Exception as e:
            logger.error(f"Pylint分析失败 {file_path}: {str(e)}")
            return {
                'issues': [{
                    'type': 'analysis_error',
                    'line': 0,
                    'description': f'分析失败: {str(e)}',
                    'severity': 'error',
                    'file': file_path,
                    'tool': 'pylint'
                }],
                'score': None,
                'stats': {}
            }
    
    def analyze_code(self, code: str, filename: str = "temp.py") -> Dict[str, Any]:
        """分析代码字符串"""
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as tmp:
                tmp.write(code)
                tmp_path = tmp.name
                
            try:
                # 分析临时文件
                result = self.analyze_file(tmp_path)
                
                # 更新文件路径为原始文件名
                for issue in result['issues']:
                    issue['file'] = filename
                    
                return result
                
            finally:
                # 清理临时文件
                try:
                    os.unlink(tmp_path)
                except:
                    pass
                    
        except Exception as e:
            logger.error(f"Pylint代码分析失败: {str(e)}")
            return {
                'issues': [{
                    'type': 'analysis_error',
                    'line': 0,
                    'description': f'分析失败: {str(e)}',
                    'severity': 'error',
                    'file': filename,
                    'tool': 'pylint'
                }],
                'score': None,
                'stats': {}
            }
    
    def _parse_pylint_issue(self, item: Dict, file_path: str) -> Optional[Dict[str, Any]]:
        """解析pylint问题"""
        try:
            line_num = item.get('line', 0)
            column = item.get('column', 0)
            message_id = item.get('message-id', '')
            message = item.get('message', '')
            symbol = item.get('symbol', '')
            type_name = item.get('type', '')
            
            # 分类错误类型和严重程度
            issue_type, severity = self._classify_pylint_issue(type_name, message_id, symbol)
            
            return {
                'type': issue_type,
                'line': line_num,
                'column': column,
                'description': f"[{message_id}] {message}",
                'severity': severity,
                'file': file_path,
                'tool': 'pylint',
                'category': 'deep_analysis',
                'message_id': message_id,
                'symbol': symbol,
                'pylint_type': type_name
            }
            
        except Exception as e:
            logger.error(f"解析pylint问题失败: {str(e)}")
            return None
    
    def _parse_text_output(self, output: str, file_path: str) -> List[Dict[str, Any]]:
        """解析文本格式输出"""
        issues = []
        try:
            lines = output.split('\n')
            current_module = ""
            
            for line in lines:
                line = line.strip()
                
                # 跳过空行和分隔线
                if not line or line.startswith('---') or line.startswith('***'):
                    continue
                    
                # 检查是否是模块行
                if line.startswith('************* Module '):
                    current_module = line.replace('************* Module ', '')
                    continue
                    
                # 尝试匹配错误行
                # 格式通常是: C0111: Missing module docstring (missing-docstring)
                # 或者: filename.py:42:0: C0111: Missing module docstring (missing-docstring)
                match = re.search(r'(?:.*?:(\d+):(?:\d+):)?\s*([CEFIRW]\d{4}):\s*(.*?)(?:\s+\((.*?)\))?$', line)
                if match:
                    line_num = int(match.group(1)) if match.group(1) else 0
                    code = match.group(2)
                    message = match.group(3)
                    symbol = match.group(4) if match.group(4) else ""
                    
                    # 确定类型和严重程度
                    type_name = self._get_type_from_code(code)
                    issue_type, severity = self._classify_pylint_issue(type_name, code, symbol)
                    
                    issues.append({
                        'type': issue_type,
                        'line': line_num,
                        'column': 0,  # 文本格式通常不提供列信息
                        'description': f"[{code}] {message}",
                        'severity': severity,
                        'file': file_path,
                        'tool': 'pylint',
                        'category': 'deep_analysis',
                        'message_id': code,
                        'symbol': symbol,
                        'pylint_type': type_name
                    })
                else:
                    # 尝试匹配旧格式: filename.py:42: [C0111] Missing module docstring
                    match = re.search(r'.*?:(\d+):\s*\[([CEFIRW]\d{4})\]\s*(.*?)$', line)
                    if match:
                        line_num = int(match.group(1))
                        code = match.group(2)
                        message = match.group(3)
                        
                        # 确定类型和严重程度
                        type_name = self._get_type_from_code(code)
                        issue_type, severity = self._classify_pylint_issue(type_name, code, "")
                        
                        issues.append({
                            'type': issue_type,
                            'line': line_num,
                            'column': 0,
                            'description': f"[{code}] {message}",
                            'severity': severity,
                            'file': file_path,
                            'tool': 'pylint',
                            'category': 'deep_analysis',
                            'message_id': code,
                            'symbol': "",
                            'pylint_type': type_name
                        })
                        
            return issues
            
        except Exception as e:
            logger.error(f"解析文本输出失败: {str(e)}")
            return []
    
    def _extract_score(self, line: str) -> Optional[float]:
        """提取pylint评分"""
        try:
            # 查找评分模式: "Your code has been rated at 8.50/10"
            import re
            match = re.search(r'rated at ([\d.]+)/10', line)
            if match:
                return float(match.group(1))
        except Exception as e:
            logger.error(f"提取评分失败: {str(e)}")
            
        return None
    
    def _extract_score_from_text(self, output: str) -> Optional[float]:
        """从文本输出中提取pylint评分"""
        try:
            for line in output.split('\n'):
                if 'Your code has been rated at' in line:
                    match = re.search(r'rated at ([\d.]+)/10', line)
                    if match:
                        return float(match.group(1))
        except Exception as e:
            logger.error(f"从文本提取评分失败: {str(e)}")
        return None
    
    def _classify_pylint_issue(self, type_name: str, message_id: str, symbol: str) -> tuple:
        """分类pylint问题类型和严重程度"""
        # 根据pylint类型分类
        if type_name == 'error':
            return 'error', 'high'
        elif type_name == 'warning':
            return 'warning', 'medium'
        elif type_name == 'refactor':
            return 'refactor', 'low'
        elif type_name == 'convention':
            return 'convention', 'low'
        elif type_name == 'info':
            return 'info', 'info'
        else:
            # 根据消息ID进一步分类
            if message_id.startswith('E'):
                return 'error', 'high'
            elif message_id.startswith('W'):
                return 'warning', 'medium'
            elif message_id.startswith('R'):
                return 'refactor', 'low'
            elif message_id.startswith('C'):
                return 'convention', 'low'
            else:
                return 'general', 'medium'
    
    def _generate_stats(self, issues: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成统计信息"""
        stats = {
            'total_issues': len(issues),
            'by_severity': {'high': 0, 'medium': 0, 'low': 0, 'info': 0},
            'by_type': {},
            'by_category': {}
        }
        
        for issue in issues:
            # 按严重程度统计
            severity = issue.get('severity', 'medium')
            if severity in stats['by_severity']:
                stats['by_severity'][severity] += 1
            
            # 按类型统计
            issue_type = issue.get('type', 'unknown')
            stats['by_type'][issue_type] = stats['by_type'].get(issue_type, 0) + 1
            
            # 按pylint类型统计
            pylint_type = issue.get('pylint_type', 'unknown')
            stats['by_category'][pylint_type] = stats['by_category'].get(pylint_type, 0) + 1
        
        return stats
    
    def get_tool_info(self) -> Dict[str, str]:
        """获取工具信息"""
        return {
            'name': self.name,
            'description': self.description,
            'category': 'deep_analysis',
            'language': 'python',
            'provides_score': True
        }

    def _get_type_from_code(self, code: str) -> str:
        """从错误代码确定类型"""
        if not code or len(code) < 1:
            return "unknown"
        
        code_type = code[0]
    
        type_map = {
            'C': 'convention',
            'R': 'refactor',
            'W': 'warning',
            'E': 'error',
            'F': 'fatal',
            'I': 'info'
        }
    
        return type_map.get(code_type, "unknown")





