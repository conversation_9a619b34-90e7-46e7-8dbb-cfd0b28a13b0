.tutoring-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.tutoring-header {
  padding: 15px 0;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 20px;
}

.tutoring-header h1 {
  font-size: 24px;
  color: #343a40;
  margin: 0;
}

.tutoring-info {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.topic-badge, .difficulty-badge {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 14px;
  font-weight: 500;
}

.topic-badge {
  background-color: #e3f2fd;
  color: #0d47a1;
}

.difficulty-badge {
  background-color: #e8f5e9;
  color: #1b5e20;
}

.difficulty-badge.初级 {
  background-color: #e8f5e9;
  color: #1b5e20;
}

.difficulty-badge.中级 {
  background-color: #fff3e0;
  color: #e65100;
}

.difficulty-badge.高级 {
  background-color: #ffebee;
  color: #b71c1c;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.message {
  display: flex;
  margin-bottom: 15px;
  max-width: 80%;
}

.user-message {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.assistant-message {
  align-self: flex-start;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 10px;
  flex-shrink: 0;
}

.user-message .message-avatar {
  background-color: #4a6cf7;
  color: white;
}

.assistant-message .message-avatar {
  background-color: #6c757d;
  color: white;
}

.message-content {
  background-color: white;
  padding: 12px 15px;
  border-radius: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
}

.user-message .message-content {
  background-color: #4a6cf7;
  color: white;
  border-top-right-radius: 0;
}

.assistant-message .message-content {
  background-color: white;
  border-top-left-radius: 0;
}

.message-timestamp {
  font-size: 12px;
  color: #adb5bd;
  margin-top: 5px;
  text-align: right;
}

.user-message .message-timestamp {
  color: rgba(255, 255, 255, 0.7);
}

.input-form {
  display: flex;
  margin-top: 20px;
  padding: 10px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.05);
}

.input-form textarea {
  flex: 1;
  padding: 12px 15px;
  border: 1px solid #ced4da;
  border-radius: 5px;
  resize: none;
  height: 60px;
  font-family: inherit;
  font-size: 16px;
}

.input-form button {
  margin-left: 10px;
  padding: 0 20px;
  background-color: #4a6cf7;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.input-form button:hover {
  background-color: #3a5bd9;
}

.input-form button:disabled {
  background-color: #adb5bd;
  cursor: not-allowed;
}

.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  color: #6c757d;
}

.spinner {
  animation: spin 1s linear infinite;
  margin-right: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.tutoring-footer {
  margin-top: 20px;
  padding: 10px;
  border-top: 1px solid #e9ecef;
  color: #6c757d;
  font-size: 14px;
  text-align: center;
}

/* 代码块样式 */
.message-content pre {
  background-color: #282c34;
  border-radius: 5px;
  padding: 10px;
  overflow-x: auto;
  margin: 10px 0;
}

.user-message .message-content pre {
  background-color: rgba(0, 0, 0, 0.3);
}

.message-content code {
  font-family: 'Fira Code', monospace;
  font-size: 14px;
}

.user-message .message-content a {
  color: #e3f2fd;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tutoring-container {
    padding: 10px;
    height: calc(100vh - 20px);
  }
  
  .message {
    max-width: 90%;
  }
  
  .input-form textarea {
    font-size: 14px;
  }
}
