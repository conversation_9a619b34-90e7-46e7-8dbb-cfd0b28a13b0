import subprocess
import tempfile
import os
import json
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class RuffAnalyzer:
    """Ruff分析器 - 中高级逻辑错误检测和自动修复"""
    
    def __init__(self):
        self.name = "Ruff"
        self.description = "中高级逻辑错误检测和自动修复"
        
    def analyze_file(self, file_path: str, auto_fix: bool = False) -> Dict[str, Any]:
        """分析单个Python文件"""
        try:
            if not file_path.endswith('.py'):
                return {'issues': [], 'fixes': []}
                
            # 检查文件是否存在
            if not os.path.exists(file_path):
                logger.error(f"文件不存在: {file_path}")
                return {'issues': [], 'fixes': []}
                
            # 运行ruff检查
            issues = self._run_ruff_check(file_path)
            
            # 如果需要自动修复，运行ruff fix
            fixes = []
            if auto_fix:
                fixes = self._run_ruff_fix(file_path)
                
            return {
                'issues': issues,
                'fixes': fixes
            }
            
        except FileNotFoundError:
            logger.error("Ruff未安装或不在PATH中")
            return {
                'issues': [{
                    'type': 'tool_error',
                    'line': 0,
                    'description': 'Ruff工具未安装',
                    'severity': 'error',
                    'file': file_path,
                    'tool': 'ruff'
                }],
                'fixes': []
            }
        except Exception as e:
            logger.error(f"Ruff分析失败 {file_path}: {str(e)}")
            return {
                'issues': [{
                    'type': 'analysis_error',
                    'line': 0,
                    'description': f'分析失败: {str(e)}',
                    'severity': 'error',
                    'file': file_path,
                    'tool': 'ruff'
                }],
                'fixes': []
            }
    
    def analyze_code(self, code: str, filename: str = "temp.py", auto_fix: bool = False) -> Dict[str, Any]:
        """分析代码字符串"""
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as tmp:
                tmp.write(code)
                tmp_path = tmp.name
                
            try:
                # 分析临时文件
                result = self.analyze_file(tmp_path, auto_fix)
                
                # 更新文件路径为原始文件名
                for issue in result['issues']:
                    issue['file'] = filename
                for fix in result['fixes']:
                    fix['file'] = filename
                    
                # 如果进行了自动修复，读取修复后的代码
                if auto_fix and result['fixes']:
                    try:
                        with open(tmp_path, 'r', encoding='utf-8') as f:
                            result['fixed_code'] = f.read()
                    except:
                        pass
                        
                return result
                
            finally:
                # 清理临时文件
                try:
                    os.unlink(tmp_path)
                except:
                    pass
                    
        except Exception as e:
            logger.error(f"Ruff代码分析失败: {str(e)}")
            return {
                'issues': [{
                    'type': 'analysis_error',
                    'line': 0,
                    'description': f'分析失败: {str(e)}',
                    'severity': 'error',
                    'file': filename,
                    'tool': 'ruff'
                }],
                'fixes': []
            }
    
    def _run_ruff_check(self, file_path: str) -> List[Dict[str, Any]]:
        """运行ruff检查"""
        try:
            # 运行ruff check --output-format=json
            result = subprocess.run(
                ['ruff', 'check', '--output-format=json', file_path],
                capture_output=True,
                text=True,
                encoding='utf-8'
            )
            
            issues = []
            if result.stdout:
                try:
                    # 解析JSON输出
                    ruff_output = json.loads(result.stdout)
                    for item in ruff_output:
                        issue = self._parse_ruff_issue(item, file_path)
                        if issue:
                            issues.append(issue)
                except json.JSONDecodeError as e:
                    logger.error(f"解析Ruff JSON输出失败: {str(e)}")
                    
            return issues
            
        except Exception as e:
            logger.error(f"运行ruff check失败: {str(e)}")
            return []
    
    def _run_ruff_fix(self, file_path: str) -> List[Dict[str, Any]]:
        """运行ruff自动修复"""
        try:
            # 运行ruff check --fix --diff
            result = subprocess.run(
                ['ruff', 'check', '--fix', '--diff', file_path],
                capture_output=True,
                text=True,
                encoding='utf-8'
            )
            
            fixes = []
            if result.stdout:
                # 解析diff输出
                fixes = self._parse_ruff_diff(result.stdout, file_path)
                
            return fixes
            
        except Exception as e:
            logger.error(f"运行ruff fix失败: {str(e)}")
            return []
    
    def _parse_ruff_issue(self, item: Dict, file_path: str) -> Optional[Dict[str, Any]]:
        """解析ruff问题"""
        try:
            location = item.get('location', {})
            line_num = location.get('row', 0)
            column = location.get('column', 0)
            
            code = item.get('code', '')
            message = item.get('message', '')
            
            # 分类错误类型和严重程度
            issue_type, severity = self._classify_ruff_issue(code, message)
            
            return {
                'type': issue_type,
                'line': line_num,
                'column': column,
                'description': f"[{code}] {message}",
                'severity': severity,
                'file': file_path,
                'tool': 'ruff',
                'category': 'advanced_logic',
                'code': code,
                'fixable': item.get('fix', {}).get('applicability') == 'automatic'
            }
            
        except Exception as e:
            logger.error(f"解析ruff问题失败: {str(e)}")
            return None
    
    def _parse_ruff_diff(self, diff_output: str, file_path: str) -> List[Dict[str, Any]]:
        """解析ruff diff输出"""
        fixes = []
        try:
            if diff_output.strip():
                fixes.append({
                    'type': 'auto_fix',
                    'description': '自动修复应用',
                    'diff': diff_output,
                    'file': file_path,
                    'tool': 'ruff'
                })
        except Exception as e:
            logger.error(f"解析ruff diff失败: {str(e)}")
            
        return fixes
    
    def _classify_ruff_issue(self, code: str, message: str) -> tuple:
        """分类ruff问题类型和严重程度"""
        # 根据ruff错误代码分类
        if code.startswith('E'):  # pycodestyle errors
            return 'style_error', 'medium'
        elif code.startswith('W'):  # pycodestyle warnings
            return 'style_warning', 'low'
        elif code.startswith('F'):  # pyflakes
            return 'logic_error', 'high'
        elif code.startswith('C'):  # complexity
            return 'complexity', 'medium'
        elif code.startswith('N'):  # naming
            return 'naming', 'low'
        elif code.startswith('D'):  # docstring
            return 'documentation', 'low'
        elif code.startswith('S'):  # security
            return 'security', 'high'
        elif code.startswith('B'):  # bugbear
            return 'potential_bug', 'high'
        elif code.startswith('A'):  # import
            return 'import_issue', 'medium'
        elif code.startswith('I'):  # isort
            return 'import_order', 'low'
        else:
            return 'other', 'medium'
    
    def get_tool_info(self) -> Dict[str, str]:
        """获取工具信息"""
        return {
            'name': self.name,
            'description': self.description,
            'category': 'advanced_logic',
            'language': 'python',
            'supports_autofix': True
        }
