from flask import Blueprint, request, jsonify
from myapp.models import User, db, Scores, Course, Comment, UserSearchHistory, Module, KnowledgePoint, LearningProgress
from myapp.front.auth import login_required
from myapp.api.resp import Resp
from datetime import datetime

front = Blueprint("front", __name__)


@front.route("/")
@login_required
def hello(userid):
    return "Hello admin" + userid


@front.route("course/star/get/<course_id>")
@login_required
def get_course_score(userid, course_id):
    score = Scores.query.filter_by(user_id=userid, course_id=course_id).first()
    if score:
        return Resp.success(data=score.score)
    else:
        return Resp.error()


@front.route("course/star/post/<course_id>/<score>", methods=['POST'])
@login_required
def give_course_score(userid, course_id, score):
    score_all = Scores.query.filter_by(course_id=course_id).all()
    has_score = Scores.query.filter_by(user_id=userid, course_id=course_id).first()
    course = Course.query.get(course_id)

    if not has_score:
        score_new = Scores(user_id=userid, course_id=course_id, score=score)
        score_time = len(score_all)
        if score_time == 0:
            course.score = score
        else:
            course.score = (course.score + score) / (score_time + 1)
        db.session.add(score_new)
        db.session.commit()
        return Resp.success()
    else:
        return Resp.error(message="已经打过分了")


@front.route("course/star/<course_id>/<score>", methods=['POST'])
@login_required
def star_course(userid, course_id, score):
    score_obj = Scores.query.filter_by(user_id=userid, course_id=course_id).first()
    if score_obj:
        score_obj.score = score
    else:
        score_obj = Scores(
            user_id=userid,
            course_id=course_id,
            score=score
        )
        db.session.add(score_obj)

    # 更新课程平均评分
    try:
        course = Course.query.get(course_id)
        scores = Scores.query.filter_by(course_id=course_id).all()

        if scores:
            # 确保所有评分都是整数类型
            score_values = [int(s.score) for s in scores]
            course.score = sum(score_values) / len(score_values)
            print(f"更新课程 {course_id} 的平均评分为: {course.score}")
        else:
            # 如果没有评分，设置为0
            course.score = 0
            print(f"课程 {course_id} 没有评分记录，设置为0")
    except Exception as e:
        print(f"更新课程评分时出错: {e}")
        # 不要让评分错误影响整个流程
        course.score = int(score)  # 使用当前评分作为备选

    # 更新知识点掌握程度
    try:
        # 确保评分是整数
        score_int = int(score)

        # 获取课程关联的知识点
        knowledge_points = KnowledgePoint.query.join(
            'courses'
        ).filter(
            Course.id == course_id
        ).all()

        print(f"课程 {course_id} 关联的知识点数量: {len(knowledge_points)}")

        # 根据评分更新掌握程度
        for kp in knowledge_points:
            progress = LearningProgress.query.filter_by(
                user_id=userid,
                course_id=course_id,
                knowledge_point_id=kp.id
            ).first()

            # 计算掌握程度 (0.0-1.0)
            mastery = score_int / 5.0  # 假设评分是1-5

            if progress:
                # 已有记录，更新掌握程度
                progress.mastery_level = mastery
                progress.last_updated = datetime.now()
                print(f"更新知识点 {kp.id} ({kp.name}) 的掌握程度为: {mastery}")
            else:
                # 创建新记录
                progress = LearningProgress(
                    user_id=userid,
                    course_id=course_id,
                    knowledge_point_id=kp.id,
                    mastery_level=mastery,
                    last_updated=datetime.now()
                )
                db.session.add(progress)
                print(f"创建知识点 {kp.id} ({kp.name}) 的掌握程度记录: {mastery}")

        # 更新个人学习报告
        try:
            from myapp.data_analysis.views import generate_and_save_personal_report
            generate_and_save_personal_report(userid)
            print(f"用户 {userid} 评分后更新个人学习报告")
        except Exception as e:
            print(f"更新个人学习报告时出错: {e}")

        # 更新教师评估报告
        try:
            # 获取课程的管理员ID
            admin_id = course.admin_id
            if admin_id:
                from myapp.data_analysis.views import generate_and_save_teaching_report
                generate_and_save_teaching_report(admin_id)
                print(f"用户 {userid} 评分后更新管理员 {admin_id} 的教学评估报告")
        except Exception as e:
            print(f"更新教学评估报告时出错: {e}")
    except Exception as e:
        print(f"更新知识点掌握程度时出错: {e}")
        # 不要让这个错误影响整个流程

    db.session.commit()
    return Resp.success()


@front.route("course/star/stats/<course_id>")
@login_required
def get_course_rating_stats(userid, course_id):
    """获取课程评分统计数据"""
    try:
        # 获取所有评分
        scores = Scores.query.filter_by(course_id=course_id).all()

        if not scores:
            return Resp.success(data={
                "total": 0,
                "distribution": {
                    "1": 0,
                    "2": 0,
                    "3": 0,
                    "4": 0,
                    "5": 0
                }
            })

        # 统计各评分数量
        distribution = {
            "1": 0,
            "2": 0,
            "3": 0,
            "4": 0,
            "5": 0
        }

        for score in scores:
            score_value = str(score.score)
            if score_value in distribution:
                distribution[score_value] += 1

        result = {
            "total": len(scores),
            "distribution": distribution
        }

        return Resp.success(data=result)
    except Exception as e:
        print(f"获取课程评分统计时出错: {e}")
        return Resp.error(message="获取评分统计失败")


# 添加评论
@front.route("course/comment/add", methods=['POST'])
@login_required
def add_comment(userid):
    course_id = request.get_json()['course_id']
    content = request.get_json()['content']
    course = Course.query.get(course_id)

    comment_new = Comment(
        course_id=course_id,
        content=content,
        from_user_id=userid,
        from_user_type=1,
        to_user_id=course.admin_id,
        to_user_type=2
    )

    db.session.add(comment_new)
    db.session.commit()

    return Resp.success()


# 收藏课程
@front.route("course/love/<course_id>", methods=["POST"])
@login_required
def user_love_course(userid, course_id):
    user = User.query.get(userid)
    course = Course.query.get(course_id)
    if not course in user.courses:
        user.courses.append(course)
        course.collect_num = len(course.users)
        db.session.commit()
        return Resp.success()
    else:
        return Resp.error()


@front.route("course/loved/<course_id>")
@login_required
def is_my_loved_course(userid, course_id):
    user = User.query.get(userid)
    course = Course.query.get(course_id)
    if course in user.courses:
        print("yes i loved it")
        return Resp.success(data="true")
    else:
        return Resp.success(data="false")


@front.route("course/dontlove/<course_id>", methods=["POST"])
@login_required
def dont_love_it_anymore(userid, course_id):
    user = User.query.get(userid)
    course = Course.query.get(course_id)
    user.courses.remove(course)
    course.collect_num = len(course.users)
    db.session.commit()
    return Resp.success()


# 用户个人信息
@front.route("user/info")
@login_required
def user_infos(userid):
    user = User.query.get(userid)
    return Resp.success(data=user.to_json())


# 修改头像
@front.route("user/info/avatar/change/<file_name>", methods=['POST'])
@login_required
def user_info(userid, file_name):
    user = User.query.get(userid)
    user.avatar = file_name
    db.session.commit()
    return Resp.success()


# 收藏列表
@front.route("course/loved/all/list")
@login_required
def all_my_loved_course(userid):
    user = User.query.get(userid)
    courses = user.courses

    return Resp.success(data=[
        course.to_json() for course in courses
    ])


# 评论列表
@front.route("my/comments/list")
@login_required
def my_comments_list(userid):
    user = User.query.get(userid)
    comments = Comment.query.filter_by(from_user_id=userid, from_user_type=1).all()
    return Resp.success(data=[
        comment.to_json() for comment in comments
    ])


# 回复我的
@front.route("my/reply/list")
@login_required
def my_replies_lists(userid):
    user = User.query.get(userid)
    comments = Comment.query.filter_by(to_user_id=userid, from_user_type=2).all()
    return Resp.success(data=[
        comment.to_json() for comment in comments
    ])


# 修改信息
@front.route("my/info/modify", methods=['POST'])
@login_required
def modify_user_info(userid):
    data = request.get_json()
    user = User.query.get(userid)

    user.name = data['name']
    user.sex = data['sex']
    user.age = data['age']
    user.describe = data['describe']
    if data['password'] != "":
        print("修改密码了")
        # 使用与注册相同的密码加密方式
        from werkzeug.security import generate_password_hash
        hashed_password = generate_password_hash(data['password'])
        user.password = hashed_password

    db.session.commit()
    return Resp.success()


# 搜索历史
@front.route("my/search/history")
@login_required
def search_history(userid):
    histories = UserSearchHistory.query.filter_by(user_id=userid).all()
    return Resp.success(data=[
        history.content for history in histories
    ])


# 搜索课程
@front.route("course/filter/<key>")
@login_required
def search_course_by_name(userid,key):
    h = UserSearchHistory(
        user_id=userid,
        content=key
    )
    db.session.add(h)
    db.session.commit()
    courses = Course.query.filter(Course.name.like('%'+key+'%')).all()
    return Resp.success(data=[course.to_json() for course in courses])


#已关注栏目
@front.route("modules/have")
@login_required
def modules_i_have(userid):
    user = User.query.get(userid)
    return Resp.success(data=[{
        "id": module.id,
        "value": module.name
    } for module in user.modules])


# 未关注的
@front.route("/modules")
@login_required
def modulesNoFollow(userid):
    all_module = Module.query.all()
    user_modules = User.query.get(userid).modules
    diff_modules = list(set(all_module).difference(set(user_modules)))

    return Resp.success(data=[{
        "id": module.id,
        "value": module.name
    } for module in diff_modules])


# 关注
@front.route("modules/follow/<id>",methods=['POST'])
@login_required
def modules_follow_it(userid,id):
    user = User.query.get(userid)
    module = Module.query.get(id)
    user.modules.append(module)
    db.session.commit()
    return Resp.success()


#取关
@front.route("modules/cancel/<id>",methods=['POST'])
@login_required
def cancel_modules(userid,id):
    user = User.query.get(userid)
    module = Module.query.get(id)
    user.modules.remove(module)
    db.session.commit()
    return Resp.success()