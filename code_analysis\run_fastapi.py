import sys
import os
import uvicorn
import logging
import importlib.util

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs", "fastapi.log")),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("run_fastapi")

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def check_dependencies():
    """检查必要的依赖是否已安装"""
    required_modules = ["fastapi", "uvicorn", "pydantic", "openai"]
    missing_modules = []

    for module in required_modules:
        if importlib.util.find_spec(module) is None:
            missing_modules.append(module)

    if missing_modules:
        logger.error(f"缺少必要的依赖: {', '.join(missing_modules)}")
        logger.error("请运行: pip install -r requirements.txt")
        return False

    return True

def check_app_exists():
    """检查FastAPI应用是否存在"""
    app_path = os.path.join(current_dir, "backend", "app.py")
    if not os.path.exists(app_path):
        logger.error(f"FastAPI应用文件不存在: {app_path}")
        return False
    return True

if __name__ == "__main__":
    # 检查依赖和应用文件
    if not check_dependencies() or not check_app_exists():
        sys.exit(1)

    # 获取端口参数
    port = 8000
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            logger.warning(f"无效的端口参数: {sys.argv[1]}，使用默认端口 {port}")

    # 确保日志目录存在
    log_dir = os.path.join(current_dir, "logs")
    os.makedirs(log_dir, exist_ok=True)

    try:
        logger.info(f"正在启动FastAPI应用，端口: {port}")
        # 启动FastAPI应用
        uvicorn.run("backend.app:app", host="127.0.0.1", port=port, log_level="info")
    except Exception as e:
        logger.error(f"启动FastAPI应用时发生错误: {str(e)}")
        sys.exit(1)
