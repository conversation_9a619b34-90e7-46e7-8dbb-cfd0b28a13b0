import subprocess
import tempfile
import os
import json
import logging
from typing import List, Dict, Any
from pathlib import Path

logger = logging.getLogger(__name__)

class PyflakesAnalyzer:
    """Pyflakes分析器 - 基础逻辑错误检测"""
    
    def __init__(self):
        self.name = "Pyflakes"
        self.description = "基础逻辑错误检测"
        
    def analyze_file(self, file_path: str) -> List[Dict[str, Any]]:
        """分析单个Python文件"""
        try:
            if not file_path.endswith('.py'):
                return []
                
            # 检查文件是否存在
            if not os.path.exists(file_path):
                logger.error(f"文件不存在: {file_path}")
                return []
                
            # 运行pyflakes
            result = subprocess.run(
                ['pyflakes', file_path],
                capture_output=True,
                text=True,
                encoding='utf-8'
            )
            
            issues = []
            if result.stdout:
                # 解析pyflakes输出
                for line in result.stdout.strip().split('\n'):
                    if line.strip():
                        issue = self._parse_pyflakes_output(line, file_path)
                        if issue:
                            issues.append(issue)
                            
            return issues
            
        except FileNotFoundError:
            logger.error("Pyflakes未安装或不在PATH中")
            return [{
                'type': 'tool_error',
                'line': 0,
                'description': 'Pyflakes工具未安装',
                'severity': 'error',
                'file': file_path,
                'tool': 'pyflakes'
            }]
        except Exception as e:
            logger.error(f"Pyflakes分析失败 {file_path}: {str(e)}")
            return [{
                'type': 'analysis_error',
                'line': 0,
                'description': f'分析失败: {str(e)}',
                'severity': 'error',
                'file': file_path,
                'tool': 'pyflakes'
            }]
    
    def analyze_code(self, code: str, filename: str = "temp.py") -> List[Dict[str, Any]]:
        """分析代码字符串"""
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as tmp:
                tmp.write(code)
                tmp_path = tmp.name
                
            try:
                # 分析临时文件
                issues = self.analyze_file(tmp_path)
                
                # 更新文件路径为原始文件名
                for issue in issues:
                    issue['file'] = filename
                    
                return issues
                
            finally:
                # 清理临时文件
                try:
                    os.unlink(tmp_path)
                except:
                    pass
                    
        except Exception as e:
            logger.error(f"Pyflakes代码分析失败: {str(e)}")
            return [{
                'type': 'analysis_error',
                'line': 0,
                'description': f'分析失败: {str(e)}',
                'severity': 'error',
                'file': filename,
                'tool': 'pyflakes'
            }]
    
    def _parse_pyflakes_output(self, line: str, file_path: str) -> Dict[str, Any]:
        """解析pyflakes输出行"""
        try:
            # Pyflakes输出格式: filename:line:col: message
            parts = line.split(':', 3)
            if len(parts) >= 3:
                line_num = int(parts[1]) if parts[1].isdigit() else 0
                message = parts[-1].strip() if len(parts) > 3 else parts[2].strip()
                
                # 分类错误类型
                issue_type = self._classify_pyflakes_issue(message)
                severity = self._get_severity(issue_type, message)
                
                return {
                    'type': issue_type,
                    'line': line_num,
                    'description': message,
                    'severity': severity,
                    'file': file_path,
                    'tool': 'pyflakes',
                    'category': 'basic_logic'
                }
                
        except Exception as e:
            logger.error(f"解析pyflakes输出失败: {str(e)}")
            
        return None
    
    def _classify_pyflakes_issue(self, message: str) -> str:
        """分类pyflakes问题类型"""
        message_lower = message.lower()
        
        if 'undefined name' in message_lower:
            return 'undefined_variable'
        elif 'imported but unused' in message_lower:
            return 'unused_import'
        elif 'redefinition of unused' in message_lower:
            return 'redefined_unused'
        elif 'duplicate argument' in message_lower:
            return 'duplicate_argument'
        elif 'before assignment' in message_lower:
            return 'used_before_assignment'
        elif 'return with argument inside generator' in message_lower:
            return 'return_in_generator'
        elif 'break outside loop' in message_lower:
            return 'break_outside_loop'
        elif 'continue not properly in loop' in message_lower:
            return 'continue_outside_loop'
        else:
            return 'logic_error'
    
    def _get_severity(self, issue_type: str, message: str) -> str:
        """根据问题类型确定严重程度"""
        high_severity = [
            'undefined_variable',
            'used_before_assignment',
            'return_in_generator',
            'break_outside_loop',
            'continue_outside_loop'
        ]
        
        medium_severity = [
            'redefined_unused',
            'duplicate_argument'
        ]
        
        if issue_type in high_severity:
            return 'high'
        elif issue_type in medium_severity:
            return 'medium'
        else:
            return 'low'
    
    def get_tool_info(self) -> Dict[str, str]:
        """获取工具信息"""
        return {
            'name': self.name,
            'description': self.description,
            'category': 'basic_logic',
            'language': 'python'
        }
