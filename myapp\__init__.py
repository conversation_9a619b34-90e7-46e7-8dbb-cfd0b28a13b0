import logging
from logging.handlers import RotatingFileHandler
import os  # 添加导入 os 模块
import pymysql

import redis
from flask import Flask, request
from flask_cors import CORS
from flask_sqlalchemy import SQLAlchemy
from flask_mail import Mail

from config import APP_ENV, config

db = SQLAlchemy()

def setupLogging(level):
    '''创建日志记录'''
    # 确保日志目录存在
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 设置日志的记录等级
    logging.basicConfig(level=level)
    # 创建日志记录器，指明日志保存的路径、每个日志文件的最大大小、保存的日志文件个数上限
    file_log_handler = RotatingFileHandler(os.path.join(log_dir, "log"), maxBytes=1024 * 1024 * 100, backupCount=10)
    # 创建日志记录的格式                 日志等级    输入日志信息的文件名 行数    日志信息
    formatter = logging.Formatter('%(levelname)s %(filename)s:%(lineno)d %(message)s')
    # 为刚创建的日志记录器设置日志记录格式
    file_log_handler.setFormatter(formatter)
    #为全局添加日志记录器
    logging.getLogger().addHandler(file_log_handler)


def create_app():

    setupLogging(config[APP_ENV].LOGGING_LEVEL)

    # flask app 实例化
    app = Flask(__name__,
                static_folder="../fronted",
                static_url_path="/html",
                )
    # 加载配置
    # app.config.from_object("config.Config")
    app.config.from_object(config[APP_ENV])

    # 检查并创建数据库
    db_host = app.config['DB_HOST']
    db_port = app.config['DB_PORT']
    db_user = app.config['DB_USER']
    db_password = app.config['DB_PASSWORD']
    db_name = app.config['DB_NAME']

    # 连接MySQL并创建数据库（如果不存在）
    try:
        connection = pymysql.connect(
            host=db_host,
            port=db_port,
            user=db_user,
            password=db_password
        )
        with connection.cursor() as cursor:
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS {db_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print(f"数据库 {db_name} 检查/创建成功")
        connection.close()
    except Exception as e:
        print(f"数据库连接或创建失败: {e}")

    CORS(app, resources=r'/*')

    # 初始化db
    db.init_app(app)

    # 路由
    @app.route('/')
    def hello_world():
        return 'Hello World!'

    # 蓝图注册
    # 前台用户登录
    from myapp.front.auth import front_auth
    app.register_blueprint(front_auth, url_prefix="/front_auth/")

    # 前台 Blueprint
    from myapp.front.view import front
    app.register_blueprint(front, url_prefix="/front")

    # 后台验证
    from myapp.admin.auth import admin_auth
    app.register_blueprint(admin_auth, url_prefix="/admin_auth/")

    # 后台Blueprint
    from myapp.admin.view import admin
    app.register_blueprint(admin, url_prefix="/admin/")

    # 文件上传与下载
    from myapp.api.file import file
    app.register_blueprint(file, url_prefix="/file/")


    # api
    from myapp.api.api import api
    app.register_blueprint(api, url_prefix="/api/")

    # 代码分析模块
    from myapp.code_analysis import code_analysis_bp
    app.register_blueprint(code_analysis_bp, url_prefix="/code_analysis/")

    # 数据分析模块
    from myapp.data_analysis import data_analysis_bp
    app.register_blueprint(data_analysis_bp, url_prefix="/data_analysis/")

    # 初始化数据库表
    with app.app_context():
        db.create_all()  # 创建所有数据库表
    return app
