from core.analyzers import CoreAnalyzer
from core.database import CodeVectorStore
from .config import settings, paths
import logging
from typing import Dict, Any, List, Optional, Tuple
import asyncio
from openai import AsyncOpenAI
import json
from pathlib import Path
import os
import re
import tempfile
import subprocess
import ast
import esprima

logger = logging.getLogger(__name__)

class CodeAuditService:
    def __init__(self):
        self.core_analyzer = None
        self.vector_store = None
        self.openai_api_key = settings.OPENAI_API_KEY
        self.api_base = settings.OPENAI_API_BASE
        self.model = settings.OPENAI_MODEL
        self.config_file = paths.config_dir / "api_config.json"

    async def ensure_initialized(self):
        """确保服务已初始化"""
        try:
            # 加载保存的配置
            await self.load_config()

            # 如果有配置，验证并更新模型
            if self.openai_api_key and self.api_base:
                try:
                    # 确保 API 基础 URL 正确
                    if not self.api_base.endswith('/v1'):
                        self.api_base = self.api_base.rstrip('/') + '/v1'

                    client = AsyncOpenAI(
                        api_key=self.openai_api_key,
                        base_url=self.api_base
                    )
                    models_response = await client.models.list()
                    available_models = [m.id for m in models_response.data]

                    # 验证当前模型
                    if not self.model or self.model not in available_models:
                        # 选择一个默认的可用模型
                        yi_models = [m for m in available_models if m.startswith('01-ai/Yi-1.5')]
                        self.model = yi_models[0] if yi_models else available_models[0]
                        await self.save_config()

                except Exception as e:
                    logger.warning(f"API验证失败，使用默认配置: {str(e)}")
                    # 如果验证失败，使用默认配置
                    if not self.model:
                        self.model = settings.OPENAI_MODEL

        except Exception as e:
            logger.error(f"服务初始化失败: {str(e)}")
            raise

    async def ensure_analysis_ready(self):
        """确保分析所需的组件已初始化"""
        if not self.vector_store:
            self.vector_store = CodeVectorStore(
                persist_directory=paths.vector_store_dir
            )

        if not self.core_analyzer:
            self.core_analyzer = CoreAnalyzer()

    async def save_config(self):
        """保存API配置"""
        config = {
            "api_key": self.openai_api_key,
            "api_base": self.api_base,
            "model": self.model
        }
        self.config_file.parent.mkdir(parents=True, exist_ok=True)
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f)

    async def load_config(self):
        """加载保存的API配置"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8-sig') as f:
                    config = json.load(f)
                self.openai_api_key = config.get("api_key")
                self.api_base = config.get("api_base")
                self.model = config.get("model")
                logger.info(f"已加载保存的配置，使用模型: {self.model}")
        except Exception as e:
            logger.error(f"加载配置失败: {str(e)}")

    async def configure_openai(self, api_key: str, api_base: str = None, model: str = None):
        """配置OpenAI API设置"""
        try:
            self.openai_api_key = api_key
            # 确保 API 基础 URL 正确
            if api_base:
                if not api_base.endswith('/v1'):
                    api_base = api_base.rstrip('/') + '/v1'
                self.api_base = api_base

            # 验证配置
            client = AsyncOpenAI(
                api_key=self.openai_api_key,
                base_url=self.api_base
            )

            try:
                # 获取可用模型列表
                models_response = await client.models.list()
                available_models = [m.id for m in models_response.data]
                logger.info(f"API配置成功，可用模型: {available_models}")

                # 如果指定了模型，验证是否可用
                if model:
                    # 对于某些API，模型ID可能需要添加前缀
                    model_variants = [
                        model,
                        f"01-ai/{model}",
                        model.replace("01-ai/", "")
                    ]

                    for variant in model_variants:
                        if variant in available_models:
                            self.model = variant
                            logger.info(f"使用指定模型: {self.model}")
                            break
                    else:
                        logger.warning(f"选择的模型 {model} 不在可用模型列表中")
                        # 选择一个默认的可用模型
                        yi_models = [m for m in available_models if m.startswith('01-ai/Yi-1.5')]
                        self.model = yi_models[0] if yi_models else available_models[0]
                else:
                    # 选择一个默认的可用模型
                    yi_models = [m for m in available_models if m.startswith('01-ai/Yi-1.5')]
                    self.model = yi_models[0] if yi_models else available_models[0]

                logger.info(f"最终使用模型: {self.model}")

                # 保存配置
                await self.save_config()

            except Exception as e:
                logger.error(f"API配置验证失败: {str(e)}")
                raise ValueError(f"API配置无效: {str(e)}")

        except Exception as e:
            logger.error(f"配置 OpenAI API 失败: {str(e)}")
            raise

    async def analyze_project(self, project_path: str) -> Dict[str, Any]:
        """分析整个项目"""
        try:
            # 1. 初始化分析器和向量数据库
            await self.ensure_initialized()
            await self.ensure_analysis_ready()

            # 2. 预处理：检查项目类型和文件有效性
            project_type = self._detect_project_type(project_path)
            logger.info(f"检测到项目类型: {project_type}")

            # 3. 获取所有有效文件
            valid_files = self._get_valid_files(project_path, project_type)
            logger.info(f"找到 {len(valid_files)} 个有效的源代码文件")

            # 4. 本地静态扫描
            logger.info("开始静态扫描...")
            suspicious_files = await self.core_analyzer._static_scan(project_path)

            # 5. 导入向量数据库
            logger.info("导入向量数据库...")
            try:
                await self.core_analyzer._import_to_vector_store(project_path)
            except Exception as e:
                logger.error(f"导入向量数据库失败: {str(e)}")
                # 继续执行，不中断流程

            # 6. 确保所有文件都被分析，即使没有检测到问题
            all_files_to_analyze = []

            # 添加已经检测到问题的文件
            analyzed_file_paths = set(file_info["file_path"] for file_info in suspicious_files)
            all_files_to_analyze.extend(suspicious_files)

            # 添加没有检测到问题的文件
            for file_path in valid_files:
                if file_path not in analyzed_file_paths:
                    # 限制文件数量，避免分析过多文件
                    if len(all_files_to_analyze) >= 10:  # 最多分析10个文件
                        break

                    language = self._check_file_type(file_path)
                    all_files_to_analyze.append({
                        "file_path": file_path,
                        "issues": [],  # 没有检测到问题
                        "language": language
                    })

            # 7. AI 深度分析
            logger.info(f"开始AI分析，共 {len(all_files_to_analyze)} 个文件...")
            try:
                results = await self.core_analyzer._ai_verify_suspicious(all_files_to_analyze)
            except Exception as e:
                logger.error(f"AI分析失败: {str(e)}")
                results = {}

            # 8. 生成最终报告
            return {
                "status": "success",
                "message": "分析完成",
                "project_type": project_type,
                "suspicious_files": suspicious_files,
                "ai_verification": results,
                "summary": {
                    "total_files": len(valid_files),
                    "analyzed_files": len(all_files_to_analyze),
                    "suspicious_files": len(suspicious_files),
                    "total_issues": sum(len(file.get("issues", [])) for file in suspicious_files),
                    "risk_level": self._calculate_risk_level(suspicious_files)
                },
                "recommendations": self._generate_recommendations(all_files_to_analyze, results)
            }

        except Exception as e:
            logger.error(f"项目分析失败: {str(e)}")
            return {
                "status": "error",
                "message": str(e),
                "project_type": "unknown",
                "suspicious_files": [],
                "ai_verification": {},
                "summary": {
                    "total_files": 0,
                    "analyzed_files": 0,
                    "suspicious_files": 0,
                    "total_issues": 0,
                    "risk_level": "unknown"
                },
                "recommendations": []
            }

    def _detect_project_type(self, project_path: str) -> str:
        """检测项目类型"""
        try:
            project_path = Path(project_path)

            # 检查特征文件
            indicators = {
                'php': ['composer.json', 'index.php', '.php'],
                'python': ['requirements.txt', 'setup.py', '.py'],
                'javascript': ['package.json', '.js', '.ts'],
                'java': ['pom.xml', 'build.gradle', '.java']
            }

            # 统计各类型文件数量
            type_counts = {k: 0 for k in indicators.keys()}

            # 遍历项目文件
            for file_path in project_path.rglob('*'):
                if file_path.is_file():
                    # 检查特征文件
                    file_name = file_path.name.lower()
                    file_ext = file_path.suffix.lower()

                    for lang, patterns in indicators.items():
                        if any(pattern in file_name or pattern == file_ext for pattern in patterns):
                            type_counts[lang] += 1

            # 根据文件数量判断项目类型
            if any(type_counts.values()):
                project_type = max(type_counts.items(), key=lambda x: x[1])[0]
                logger.info(f"检测到项目类型: {project_type}")
                return project_type

            return "unknown"

        except Exception as e:
            logger.error(f"项目类型检测失败: {str(e)}")
            return "unknown"

    def _get_valid_files(self, project_path: str, project_type: str) -> List[str]:
        """获取指定项目类型的有效文件"""
        try:
            project_path = Path(project_path)
            valid_files = []

            # 定义项目类型对应的文件扩展名
            type_extensions = {
                'php': ['.php'],
                'python': ['.py', '.pyw'],
                'javascript': ['.js', '.jsx', '.ts', '.tsx'],
                'java': ['.java', '.jsp']
            }

            # 获取当前项目类型支持的扩展名
            valid_extensions = type_extensions.get(project_type, [])
            if project_type == "auto":
                valid_extensions = [ext for exts in type_extensions.values() for ext in exts]

            # 忽略的目录
            ignore_dirs = {
                'node_modules', 'venv', '.git', '.svn', '__pycache__',
                'vendor', 'dist', 'build', 'target', 'tests', 'test'
            }

            # 遍历项目文件
            for file_path in project_path.rglob('*'):
                try:
                    # 检查是否在忽略目录中
                    if any(ignore_dir in file_path.parts for ignore_dir in ignore_dirs):
                        continue

                    # 检查是否为文件
                    if not file_path.is_file():
                        continue

                    # 检查是否为隐藏文件
                    if file_path.name.startswith('.'):
                        continue

                    # 检查扩展名
                    if file_path.suffix.lower() in valid_extensions:
                        # 检查文件是否可读
                        try:
                            with open(file_path, 'r', encoding='utf-8'):
                                pass
                            valid_files.append(str(file_path))
                        except (UnicodeDecodeError, PermissionError):
                            continue

                except Exception as e:
                    logger.error(f"处理文件失败 {file_path}: {str(e)}")
                    continue

            logger.info(f"找到 {len(valid_files)} 个有效的源代码文件")
            return valid_files

        except Exception as e:
            logger.error(f"获取有效文件失败: {str(e)}")
            return []

    async def _import_suspicious_to_vector_store(self, suspicious_files: List[Dict[str, Any]]):
        """仅导入可疑文件及其相关文件到向量数据库"""
        try:
            code_snippets = []
            for file_info in suspicious_files:
                file_path = Path(file_info["file_path"])  # 转换为 Path 对象

                # 获取文件内容，尝试不同的编码
                content = None
                encodings = ['utf-8', 'gbk', 'latin1']
                for encoding in encodings:
                    try:
                        with open(file_path, "r", encoding=encoding) as f:
                            content = f.read()
                        break
                    except UnicodeDecodeError:
                        continue

                if content is None:
                    logger.warning(f"无法读取文件 {file_path}，跳过")
                    continue

                # 添加可疑文件
                code_snippets.append({
                    "code": content,
                    "file_path": str(file_path),  # 转换回字符串
                    "line_start": 1,
                    "line_end": len(content.splitlines()),
                    "metadata": {
                        "type": "suspicious",
                        "issues_json": json.dumps(file_info.get("issues", [])),
                        "language": file_info.get("language", "unknown")
                    }
                })

                # 获取相关文件
                related_files = self._get_related_files(str(file_path))  # 传入字符串
                for related_file in related_files:
                    related_path = Path(related_file)
                    if related_path != file_path:
                        # 尝试不同的编码读取相关文件
                        related_content = None
                        for encoding in encodings:
                            try:
                                with open(related_path, "r", encoding=encoding) as f:
                                    related_content = f.read()
                                break
                            except UnicodeDecodeError:
                                continue

                        if related_content is None:
                            logger.warning(f"无法读取相关文件 {related_path}，跳过")
                            continue

                        code_snippets.append({
                            "code": related_content,
                            "file_path": str(related_path),  # 转换回字符串
                            "line_start": 1,
                            "line_end": len(related_content.splitlines()),
                            "metadata": {
                                "type": "related",
                                "related_to": str(file_path),  # 转换回字符串
                                "language": self._check_file_type(str(related_path))
                            }
                        })

            # 批量导入向量数据库
            if code_snippets:
                await self.vector_store.add_code_to_store(code_snippets)

        except Exception as e:
            logger.error(f"导入向量数据库失败: {str(e)}")
            raise

    def _generate_empty_report(self) -> Dict[str, Any]:
        """生成空的分析报告"""
        return {
            "summary": {
                "total_files": 0,
                "total_issues": 0,
                "risk_level": "low",
                "scan_time": None,
                "project_info": {
                    "name": None,
                    "path": None,
                    "files_scanned": 0
                }
            },
            "details": {
                "suspicious_files": [],
                "ai_verification": {},
                "scan_coverage": {
                    "total_files": 0,
                    "scanned_files": 0,
                    "coverage_rate": 0
                }
            },
            "recommendations": []
        }

    def _generate_report(self, file_infos: List[Dict[str, Any]], ai_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成分析报告"""
        try:
            # 初始化报告结构
            report = {
                "summary": {
                    "total_issues": 0,
                    "risk_level": "info",
                    "severity_counts": {
                        "high": 0,
                        "medium": 0,
                        "low": 0,
                        "info": 0
                    }
                },
                "details": {
                    "suspicious_files": []
                },
                "recommendations": []
            }

            # 处理每个文件
            for file_info in file_infos:
                file_path = file_info.get("file_path", "unknown")
                issues = file_info.get("issues", [])
                
                # 确保issues是列表
                if not isinstance(issues, list):
                    issues = []
                
                # 计算问题数量
                report["summary"]["total_issues"] += len(issues)
                
                # 更新严重程度计数
                for issue in issues:
                    severity = issue.get("severity", "info")
                    report["summary"]["severity_counts"][severity] = report["summary"]["severity_counts"].get(severity, 0) + 1
                
                # 确定风险等级
                if any(issue.get("severity") == "high" for issue in issues):
                    report["summary"]["risk_level"] = "high"
                elif report["summary"]["risk_level"] != "high" and any(issue.get("severity") == "medium" for issue in issues):
                    report["summary"]["risk_level"] = "medium"
                elif report["summary"]["risk_level"] not in ["high", "medium"] and any(issue.get("severity") == "low" for issue in issues):
                    report["summary"]["risk_level"] = "low"
                
                # 添加文件详情
                file_detail = {
                    "file_path": file_path,
                    "issues": issues,
                    "language": file_info.get("language", "unknown")
                }
                
                # 添加Python静态分析结果
                python_static = file_info.get("python_static")
                if python_static:
                    file_detail["python_static"] = python_static
                
                report["details"]["suspicious_files"].append(file_detail)
                
                # 处理AI分析结果
                ai_result = ai_results.get(file_path, {})
                if ai_result:
                    # 获取AI分析中的建议
                    ai_analysis = ai_result.get("ai_analysis", {})
                    analysis = ai_analysis.get("analysis", {})
                    
                    # 添加AI建议
                    recommendations = analysis.get("recommendations", [])
                    for rec in recommendations:
                        report["recommendations"].append({
                            "file": file_path,
                            "issue": rec.get("issue_type", "AI建议"),
                            "solution": rec.get("description", "")
                        })
            
            # 如果没有问题，添加一个默认建议
            if not report["recommendations"]:
                report["recommendations"].append({
                    "file": "general",
                    "issue": "代码质量",
                    "solution": "代码分析未发现明显问题，继续保持良好的编码习惯。"
                })
            
            return report
            
        except Exception as e:
            logger.error(f"生成报告失败: {str(e)}")
            # 返回空报告
            return self._generate_empty_report()

    def _calculate_risk_level(self, suspicious_files: List[Dict]) -> str:
        """计算整体风险等级"""
        try:
            high_count = 0
            medium_count = 0
            low_count = 0

            for file in suspicious_files:
                for issue in file.get("issues", []):
                    severity = issue.get("severity", "").lower()
                    if severity == "high":
                        high_count += 1
                    elif severity == "medium":
                        medium_count += 1
                    elif severity == "low":
                        low_count += 1

            if high_count > 0:
                return "high"
            elif medium_count > 0:
                return "medium"
            elif low_count > 0:
                return "low"
            else:
                return "info"

        except Exception:
            return "unknown"

    def _generate_recommendations(self, suspicious_files: List[Dict], ai_results: Dict) -> List[Dict]:
        """生成修复建议"""
        recommendations = []
        try:
            for file in suspicious_files:
                file_path = file.get("file_path", "")
                ai_result = ai_results.get(file_path, {}).get("ai_analysis", {})

                if isinstance(ai_result, dict):
                    recs = ai_result.get("recommendations", [])
                    for rec in recs:
                        if isinstance(rec, dict):
                            recommendations.append({
                                "file": file_path,
                                "issue": rec.get("issue", ""),
                                "solution": rec.get("solution", "")
                            })

        except Exception as e:
            logger.error(f"生成修复建议失败: {str(e)}")

        return recommendations

    def _get_related_files(self, file_path: str) -> List[str]:
        """获取与指定文件相关的文件"""
        try:
            related_files = []
            file_path = Path(file_path)  # 转换为 Path 对象
            file_dir = file_path.parent

            # 1. 检查同目录下的文件
            for f in file_dir.iterdir():
                if f.is_file() and f != file_path:
                    related_files.append(str(f))  # 转换为字符串

            # 2. 检查包含关系
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 检查 PHP 的 include/require
            if file_path.suffix.lower() == '.php':
                includes = re.findall(r'(?:include|require)(?:_once)?\s*[\'"]([^\'"]+)[\'"]', content)
                for inc in includes:
                    inc_path = file_dir / inc  # 使用 Path 对象的 / 运算符
                    if inc_path.exists():
                        related_files.append(str(inc_path))

            # 检查 Python 的 import
            elif file_path.suffix.lower() == '.py':
                imports = re.findall(r'(?:from|import)\s+([\w.]+)', content)
                for imp in imports:
                    imp_parts = imp.split('.')
                    imp_path = file_dir.joinpath(*imp_parts).with_suffix('.py')
                    if imp_path.exists():
                        related_files.append(str(imp_path))

            # 检查 JavaScript 的 require/import
            elif file_path.suffix.lower() in ('.js', '.jsx', '.ts', '.tsx'):
                imports = re.findall(r'(?:require|import)\s*[\(\{]\s*[\'"]([^\'"]+)[\'"]', content)
                for imp in imports:
                    imp_path = file_dir / imp  # 基本路径
                    # 检查多个可能的扩展名
                    possible_exts = ['.js', '.jsx', '.ts', '.tsx']
                    for ext in possible_exts:
                        full_path = imp_path.with_suffix(ext)
                        if full_path.exists():
                            related_files.append(str(full_path))
                            break

            return list(set(related_files))  # 去重

        except Exception as e:
            logger.error(f"获取相关文件失败 {file_path}: {str(e)}")
            return []

    def _check_file_type(self, file_path: str) -> str:
        """检查文件类型"""
        ext = Path(file_path).suffix.lower()  # 使用 Path 对象获取扩展名
        language_map = {
            '.py': 'python',
            '.js': 'javascript',
            '.ts': 'typescript',
            '.jsx': 'react',
            '.tsx': 'react',
            '.php': 'php',
            '.java': 'java',
            '.cs': 'csharp',
            '.go': 'go',
            '.rb': 'ruby',
            '.html': 'html',
            '.css': 'css',
            '.sql': 'sql'
        }
        return language_map.get(ext, 'unknown')

    async def analyze_code(self, code: str, language: str, filename: str, api_key: str = None, api_base: str = None) -> Dict[str, Any]:
        """分析单个代码文件"""
        try:
            # 初始化
            await self.ensure_initialized()
            await self.ensure_analysis_ready()

            # 进行代码分析
            analysis_result = self.core_analyzer.analyze_code_string(code, language, filename)
            issues = analysis_result.get('issues', [])
            python_static = analysis_result.get('python_static')

            # 创建文件信息对象
            file_info = {
                "file_path": filename,
                "issues": issues,
                "language": language,
                "content": code,  # 直接将代码内容包含在文件信息中
                "python_static": python_static  # 添加Python静态分析结果
            }

            # 无论是否发现问题，都调用AI分析
            logger.info("开始AI分析...")
            ai_results = {}
            try:
                # 将单个文件包装成列表传递给AI验证方法
                single_file_results = await self.core_analyzer._ai_verify_single_file(file_info, code)

                # 确保结果格式正确
                if single_file_results and filename in single_file_results:
                    ai_results = single_file_results
                else:
                    # 如果返回格式不符合预期，进行转换
                    logger.warning("AI分析返回格式不符合预期，进行转换")
                    ai_results = {
                        filename: single_file_results.get(file_info["file_path"], {})
                    }
            except Exception as e:
                logger.error(f"AI分析失败: {str(e)}")

            # 生成报告
            report = self._generate_report([file_info], ai_results)

            # 添加详细的Python静态分析结果日志
            if language == 'python' and python_static:
                logger.info("Python静态分析详细结果:")
                # 记录Pyflakes结果
                pyflakes_issues = python_static.get('pyflakes', {}).get('issues', [])
                logger.info(f"Pyflakes发现 {len(pyflakes_issues)} 个问题")
                for i, issue in enumerate(pyflakes_issues[:10]):  # 只记录前10个问题避免日志过长
                    logger.info(f"Pyflakes问题 {i+1}: {issue.get('description')} 在第 {issue.get('line')} 行")
                
                # 记录Ruff结果
                ruff_issues = python_static.get('ruff', {}).get('issues', [])
                ruff_fixes = python_static.get('ruff', {}).get('fixes', [])
                logger.info(f"Ruff发现 {len(ruff_issues)} 个问题，提供 {len(ruff_fixes)} 个修复")
                for i, issue in enumerate(ruff_issues[:10]):
                    logger.info(f"Ruff问题 {i+1}: {issue.get('description')} 在第 {issue.get('line')} 行")
                
                # 记录Pylint结果
                pylint_issues = python_static.get('pylint', {}).get('issues', [])
                pylint_score = python_static.get('pylint', {}).get('score')
                logger.info(f"Pylint评分: {pylint_score}, 发现 {len(pylint_issues)} 个问题")
                for i, issue in enumerate(pylint_issues[:10]):
                    logger.info(f"Pylint问题 {i+1}: {issue.get('description')} 在第 {issue.get('line')} 行")
                
                # 记录总结信息
                summary = python_static.get('summary', {})
                logger.info(f"静态分析总结: 总问题数 {summary.get('total_issues')}, 风险等级 {summary.get('risk_level')}")
                logger.info(f"严重程度统计: {summary.get('severity_counts', {})}")
                
                # 将Python静态分析结果添加到报告中
                # 1. 添加到summary中
                report["summary"]["python_static"] = summary
                
                # 2. 更新总问题数
                if "total_issues" in report["summary"] and summary.get("total_issues"):
                    report["summary"]["total_issues"] += summary.get("total_issues", 0)
                
                # 3. 更新风险等级
                if summary.get("risk_level") in ["high", "medium"] and report["summary"].get("risk_level") in ["low", "info"]:
                    report["summary"]["risk_level"] = summary.get("risk_level")
                elif summary.get("risk_level") == "high" and report["summary"].get("risk_level") == "medium":
                    report["summary"]["risk_level"] = "high"
                
                # 4. 将Python静态分析结果添加到报告的详细信息中
                if "details" in report and "suspicious_files" in report["details"] and len(report["details"]["suspicious_files"]) > 0:
                    # 找到当前文件的详细信息
                    for file_detail in report["details"]["suspicious_files"]:
                        if file_detail["file_path"] == filename:
                            # 添加Python静态分析结果
                            file_detail["python_static"] = python_static
                            
                            # 合并issues - 确保issues是一个列表
                            if not isinstance(file_detail["issues"], list):
                                file_detail["issues"] = []
                                
                            for tool in ["pyflakes", "ruff", "pylint"]:
                                tool_issues = python_static.get(tool, {}).get("issues", [])
                                for issue in tool_issues:
                                    # 转换为标准格式
                                    std_issue = {
                                        "type": issue.get("type", tool),
                                        "description": issue.get("description", ""),
                                        "severity": issue.get("severity", "medium"),
                                        "line": issue.get("line", 0),
                                        "tool": tool
                                    }
                                    file_detail["issues"].append(std_issue)
                            break
                
                # 5. 添加Python静态分析的建议到报告的建议中
                if "recommendations" in report and summary.get('recommendations'):
                    for rec in summary.get('recommendations', []):
                        report["recommendations"].append({
                            "file": filename,
                            "issue": "Python静态分析",
                            "solution": rec
                        })

            # 记录AI分析结果
            ai_result = ai_results.get(filename, {})
            logger.info(f"AI分析结果: {json.dumps(ai_result, ensure_ascii=False)[:200]}...")
            logger.info(f"AI分析结果状态: {ai_result.get('ai_analysis', {}).get('status', 'unknown')}")

            # 为单文件分析创建特殊格式的 ai_verification
            # 使用文件路径作为键，这样前端可以像多文件分析一样处理
            ai_verification = {
                filename: ai_result
            }
            logger.info(f"AI验证结果格式化后: {json.dumps(ai_verification, ensure_ascii=False)[:200]}...")

            # 构造响应
            response = {
                "status": "success",
                "suspicious_files": [  # 添加这个字段，确保前端可以正确识别
                    {
                        "file_path": filename,
                        "issues": issues if isinstance(issues, list) else [],  # 确保issues是一个列表
                        "language": language,
                        "python_static": python_static
                    }
                ],
                "issues": issues if isinstance(issues, list) else [],  # 确保issues是一个列表
                "python_static": python_static,  # 添加Python静态分析结果
                "ai_verification": ai_verification,  # 使用格式化后的 ai_verification
                "report": report,
                "summary": report["summary"],
                "details": report["details"],
                "recommendations": report["recommendations"]
            }

            # 记录响应大小
            logger.info(f"响应大小: {len(str(response))} 字符")

            return response

        except Exception as e:
            logger.error(f"代码分析失败: {str(e)}")
            logger.exception("详细错误信息:")

            # 生成空报告
            empty_report = self._generate_empty_report()

            # 构造错误响应
            error_response = {
                "status": "error",
                "message": f"代码分析失败: {str(e)}",
                "issues": [],
                "python_static": None,  # 添加空的Python静态分析结果
                "ai_verification": {"error": {}},  # 使用与多文件分析一致的格式
                "report": empty_report,
                "summary": empty_report["summary"],
                "details": empty_report["details"],
                "recommendations": empty_report["recommendations"]
            }

            # 记录错误响应
            logger.info(f"错误响应大小: {len(str(error_response))} 字符")

            return error_response

    def scan_project(self, project_path: str) -> List[str]:
        """扫描项目文件"""
        valid_files = []
        try:
            # 添加调试日志
            logger.debug(f"开始扫描项目: {project_path}")

            for root, _, files in os.walk(project_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    # 添加调试日志
                    logger.debug(f"检查文件: {file_path}")
                    if file_path.endswith(('.php', '.blade.php', '.js', '.ts', '.tsx', '.py')):
                        valid_files.append(file_path)
                        logger.debug(f"添加有效文件: {file_path}")

            logger.info(f"找到 {len(valid_files)} 个有效的源代码文件")
            return valid_files

        except Exception as e:
            logger.error(f"扫描项目失败: {str(e)}")
            return []

    async def check_syntax(self, code: str, language: str) -> Dict[str, Any]:
        """检查代码语法错误

        Args:
            code: 代码内容
            language: 编程语言

        Returns:
            包含语法检查结果的字典
        """
        try:
            # 初始化
            await self.ensure_initialized()

            # 根据语言选择不同的语法检查方法
            if language == 'python':
                syntax_result = self._check_python_syntax(code)
            elif language in ['javascript', 'typescript', 'react']:
                syntax_result = self._check_js_syntax(code)
            elif language == 'php':
                syntax_result = self._check_php_syntax(code)
            elif language == 'java':
                syntax_result = self._check_java_syntax(code)
            else:
                # 对于不支持的语言，返回无法检查的结果
                return {
                    "status": "warning",
                    "has_errors": False,
                    "message": f"不支持对 {language} 语言进行语法检查",
                    "errors": []
                }

            return syntax_result

        except Exception as e:
            logger.error(f"语法检查失败: {str(e)}")
            return {
                "status": "error",
                "has_errors": True,
                "message": f"语法检查过程中发生错误: {str(e)}",
                "errors": [{
                    "line": 0,
                    "column": 0,
                    "message": str(e),
                    "severity": "error"
                }]
            }

    def _check_python_syntax(self, code: str) -> Dict[str, Any]:
        """检查Python代码的语法

        Args:
            code: Python代码内容

        Returns:
            包含语法检查结果的字典
        """
        try:
            # 使用Python内置的ast模块解析代码
            ast.parse(code)

            # 如果没有抛出异常，说明语法正确
            return {
                "status": "success",
                "has_errors": False,
                "message": "Python代码语法正确",
                "errors": []
            }

        except SyntaxError as e:
            # 捕获语法错误
            return {
                "status": "error",
                "has_errors": True,
                "message": "Python代码存在语法错误",
                "errors": [{
                    "line": e.lineno or 0,
                    "column": e.offset or 0,
                    "message": str(e),
                    "severity": "error"
                }]
            }
        except Exception as e:
            # 捕获其他错误
            return {
                "status": "error",
                "has_errors": True,
                "message": f"Python代码检查失败: {str(e)}",
                "errors": [{
                    "line": 0,
                    "column": 0,
                    "message": str(e),
                    "severity": "error"
                }]
            }

    def _check_js_syntax(self, code: str) -> Dict[str, Any]:
        """检查JavaScript/TypeScript代码的语法

        Args:
            code: JS/TS代码内容

        Returns:
            包含语法检查结果的字典
        """
        try:
            # 使用esprima解析JavaScript代码
            esprima.parseScript(code)

            # 如果没有抛出异常，说明语法正确
            return {
                "status": "success",
                "has_errors": False,
                "message": "JavaScript代码语法正确",
                "errors": []
            }

        except Exception as e:
            # 尝试从错误消息中提取行号和列号
            error_msg = str(e)
            line_match = re.search(r'Line (\d+)', error_msg)
            column_match = re.search(r'Column (\d+)', error_msg)

            line = int(line_match.group(1)) if line_match else 0
            column = int(column_match.group(1)) if column_match else 0

            return {
                "status": "error",
                "has_errors": True,
                "message": "JavaScript代码存在语法错误",
                "errors": [{
                    "line": line,
                    "column": column,
                    "message": error_msg,
                    "severity": "error"
                }]
            }

    def _check_php_syntax(self, code: str) -> Dict[str, Any]:
        """检查PHP代码的语法

        Args:
            code: PHP代码内容

        Returns:
            包含语法检查结果的字典
        """
        try:
            # 创建临时文件存储PHP代码
            with tempfile.NamedTemporaryFile(suffix='.php', mode='w', delete=False) as tmp:
                tmp.write(code)
                tmp_path = tmp.name

            try:
                # 使用PHP命令行进行语法检查
                result = subprocess.run(['php', '-l', tmp_path],
                                    capture_output=True,
                                    text=True)

                # 删除临时文件
                os.unlink(tmp_path)

                # 检查结果
                if "No syntax errors detected" in result.stdout:
                    return {
                        "status": "success",
                        "has_errors": False,
                        "message": "PHP代码语法正确",
                        "errors": []
                    }
                else:
                    # 解析错误信息
                    error_lines = result.stderr.strip().split('\n')
                    errors = []

                    for line in error_lines:
                        if line and not line.startswith('No syntax errors detected'):
                            # 尝试提取行号
                            line_match = re.search(r'on line (\d+)', line)
                            line_num = int(line_match.group(1)) if line_match else 0

                            errors.append({
                                "line": line_num,
                                "column": 0,  # PHP通常不提供列号
                                "message": line,
                                "severity": "error"
                            })

                    return {
                        "status": "error",
                        "has_errors": True,
                        "message": "PHP代码存在语法错误",
                        "errors": errors
                    }

            except subprocess.CalledProcessError as e:
                # 删除临时文件
                os.unlink(tmp_path)

                return {
                    "status": "error",
                    "has_errors": True,
                    "message": f"PHP语法检查失败: {str(e)}",
                    "errors": [{
                        "line": 0,
                        "column": 0,
                        "message": str(e),
                        "severity": "error"
                    }]
                }

        except Exception as e:
            return {
                "status": "error",
                "has_errors": True,
                "message": f"PHP代码检查失败: {str(e)}",
                "errors": [{
                    "line": 0,
                    "column": 0,
                    "message": str(e),
                    "severity": "error"
                }]
            }

    def _check_java_syntax(self, code: str) -> Dict[str, Any]:
        """检查Java代码的语法

        Args:
            code: Java代码内容

        Returns:
            包含语法检查结果的字典
        """
        try:
            # 由于Java需要编译环境，这里使用简单的正则表达式检查一些常见错误
            # 这不是完整的语法检查，只是一个基本实现

            # 检查是否缺少分号
            missing_semicolon = re.search(r'[a-zA-Z0-9"\']\s*$', code, re.MULTILINE)
            # 检查括号是否匹配
            brackets_count = code.count('{') - code.count('}')
            parentheses_count = code.count('(') - code.count(')')

            errors = []

            if missing_semicolon:
                line_num = code[:missing_semicolon.start()].count('\n') + 1
                errors.append({
                    "line": line_num,
                    "column": 0,
                    "message": "可能缺少分号",
                    "severity": "warning"
                })

            if brackets_count != 0:
                errors.append({
                    "line": 0,
                    "column": 0,
                    "message": f"花括号不匹配，差值: {brackets_count}",
                    "severity": "error"
                })

            if parentheses_count != 0:
                errors.append({
                    "line": 0,
                    "column": 0,
                    "message": f"圆括号不匹配，差值: {parentheses_count}",
                    "severity": "error"
                })

            if errors:
                return {
                    "status": "error",
                    "has_errors": True,
                    "message": "Java代码可能存在语法错误",
                    "errors": errors
                }
            else:
                return {
                    "status": "success",
                    "has_errors": False,
                    "message": "未检测到明显的Java语法错误",
                    "errors": []
                }

        except Exception as e:
            return {
                "status": "error",
                "has_errors": True,
                "message": f"Java代码检查失败: {str(e)}",
                "errors": [{
                    "line": 0,
                    "column": 0,
                    "message": str(e),
                    "severity": "error"
                }]
            }
