// JavaScript 安全漏洞示例

// DOM XSS 漏洞示例
function domXssExample() {
    const userInput = new URLSearchParams(window.location.search).get('name');
    document.getElementById('greeting').innerHTML = 'Hello, ' + userInput; // 直接插入用户输入到 DOM
}

// 不安全的 eval 使用示例
function evalExample() {
    const userCode = document.getElementById('userCode').value;
    eval(userCode); // 直接执行用户输入的代码
}

// 原型污染示例
function prototypePolluteExample(userInput) {
    const obj = {};
    const data = JSON.parse(userInput);
    
    // 不安全的对象合并
    for (const key in data) {
        obj[key] = data[key];
    }
    
    return obj;
}

// 不安全的正则表达式示例 (可能导致 ReDoS)
function regexExample(userInput) {
    const regex = /^(a+)+$/; // 易受 ReDoS 攻击的正则表达式
    return regex.test(userInput);
}

// 不安全的 localStorage 使用
function storeUserData(userData) {
    localStorage.setItem('userData', userData); // 存储未验证的用户数据
}

// 不安全的 AJAX 请求
function fetchUserData(userId) {
    const url = '/api/users/' + userId; // 未验证的用户输入
    fetch(url)
        .then(response => response.json())
        .then(data => console.log(data));
}
