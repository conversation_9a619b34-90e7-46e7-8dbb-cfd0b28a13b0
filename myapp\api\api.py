from flask import Blueprint, request, jsonify
from myapp.models import Module, db, Course, Comment, LearningActivity, KnowledgePoint
from myapp.api.resp import Resp
from sqlalchemy import desc
from myapp.admin.auth import admin_required
from sqlalchemy.sql.expression import func
from datetime import datetime


api = Blueprint("api", __name__)


@api.route("/modules", methods=["GET"])
def get_modules():
    try:
        print("请求获取模块列表")
        all_module = Module.query.all()
        print(f"查询到{len(all_module)}个模块")

        result = [{
            "id": module.id,
            "value": module.name
        } for module in all_module]

        print(f"返回模块数据: {result}")
        return Resp.success(data=result)
    except Exception as e:
        print(f"获取模块列表时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return Resp.error(message=f"获取模块列表失败: {str(e)}")

@api.route("/modules", methods=["POST"])
@admin_required
def add_module(adminid):
    try:
        print(f"管理员{adminid}尝试添加模块")
        data = request.get_json()
        print(f"接收到的数据: {data}")

        # 验证必要字段
        if not data.get('name'):
            return Resp.error(message="模块名称不能为空")

        # 创建模块
        module = Module(name=data.get('name'))

        print(f"准备添加模块: 名称={module.name}")
        db.session.add(module)
        db.session.commit()

        # 重新查询以获取完整数据
        module = Module.query.get(module.id)

        result = {
            "id": module.id,
            "value": module.name
        }

        print(f"模块添加成功: {result}")
        return Resp.success(data=result)
    except Exception as e:
        db.session.rollback()
        print(f"添加模块时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return Resp.error(message=f"添加模块失败: {str(e)}")

@api.route("/modules/<int:module_id>", methods=["PUT"])
@admin_required
def update_module(adminid, module_id):
    try:
        print(f"管理员{adminid}尝试更新模块ID: {module_id}")
        module = Module.query.get(module_id)

        if not module:
            print(f"模块不存在")
            return Resp.error(message="模块不存在")

        data = request.get_json()
        print(f"接收到的数据: {data}")

        # 验证必要字段
        if not data.get('name'):
            return Resp.error(message="模块名称不能为空")

        # 更新模块
        old_name = module.name
        module.name = data.get('name')

        print(f"更新模块: ID={module.id}, 名称从'{old_name}'变为'{module.name}'")
        db.session.commit()

        # 重新查询以获取完整数据
        module = Module.query.get(module_id)

        result = {
            "id": module.id,
            "value": module.name
        }

        print(f"模块更新成功: {result}")
        return Resp.success(data=result)
    except Exception as e:
        db.session.rollback()
        print(f"更新模块时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return Resp.error(message=f"更新模块失败: {str(e)}")

@api.route("/modules/<int:module_id>", methods=["DELETE"])
@admin_required
def delete_module(adminid, module_id):
    try:
        print(f"管理员{adminid}尝试删除模块ID: {module_id}")
        module = Module.query.get(module_id)

        if not module:
            print(f"模块不存在")
            return Resp.error(message="模块不存在")

        # 检查是否有关联的知识点
        knowledge_points = KnowledgePoint.query.filter_by(module_id=module_id).all()
        if knowledge_points:
            print(f"模块有{len(knowledge_points)}个关联的知识点，无法删除")
            return Resp.error(message=f"该模块有{len(knowledge_points)}个关联的知识点，请先删除或修改这些知识点")

        # 删除模块
        print(f"删除模块: {module.name}")
        db.session.delete(module)
        db.session.commit()

        return Resp.success(message="模块删除成功")
    except Exception as e:
        db.session.rollback()
        print(f"删除模块时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return Resp.error(message=f"删除模块失败: {str(e)}")


@api.route("/course/get/<id>")
def get_course(id):
    course = Course.query.get(id)
    course.view_num = 1 if course.view_num==None else course.view_num+1

    # 获取用户ID（如果有）
    user_id = None
    auth_header = request.headers.get('Authorization')
    if auth_header and auth_header.startswith('Bearer '):
        from myapp.front.auth import decode_auth_token
        token = auth_header.split(' ')[1]
        try:
            user_id = decode_auth_token(token)
        except:
            pass

    # 如果有用户ID，记录浏览活动
    if user_id:
        activity = LearningActivity(
            user_id=user_id,
            course_id=id,
            activity_type='view',
            start_time=datetime.now(),
            duration=0
        )
        db.session.add(activity)
        db.session.commit()

    return Resp.success(data=course.to_json())


@api.route("/course/all")
def all_course():
    try:
        print("请求获取所有课程")
        courses = Course.query.all()
        print(f"查询到{len(courses)}个课程")

        result = [course.to_json() for course in courses]

        print(f"返回课程数据: {[c['name'] for c in result]}")  # 只打印课程名称，避免日志过长
        return Resp.success(data=result)
    except Exception as e:
        print(f"获取所有课程时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return Resp.error(message=f"获取所有课程失败: {str(e)}")


@api.route("/comment/all/<course_id>")
def get_comments(course_id):
    comments = Comment.query.filter_by(course_id=course_id).all()
    list = [
        comment.to_json() for comment in comments
    ]
    return  Resp.success(data=list)


@api.route("/comment/get/<id>")
def get_comment(id):
    comment = Comment.query.get(id)
    return Resp.success(data=comment.content)


# 根据模块获取课程
@api.route("course/get/from/module/<module>")
def get_courses_form_module(module):
    if module=="1":
        courses = Course.query.order_by(desc('view_num')).all()
    elif module=="2":
        courses = Course.query.order_by(func.random()).limit(5).all()
    else:
        module = Module.query.filter_by(name=module).first()
        courses = Course.query.filter_by(module=module).all()
    print(courses)
    return Resp.success(data=[course.to_json() for course in courses])


