import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import ElementUI from 'element-ui';
import axios from 'axios'
import 'element-ui/lib/theme-chalk/index.css';

//注册组件库
Vue.use(ElementUI);

Vue.config.productionTip = false

Vue.prototype.$axios = axios

axios.defaults.baseURL = 'http://127.0.0.1:5000/'

axios.interceptors.request.use((config) => {
  // console.log(config.headers.Authorization)
  if (!config.headers.Authorization) {
    // 优先使用token_admin，如果不存在则使用token
    const token = localStorage.getItem('token_admin') || localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = 'Bearer ' + token
    }
  }
  // console.log(config.headers.Authorization)
  console.log('发送请求:', config.method.toUpperCase(), config.url);

  // 初始化重试计数器
  if (config.retry && !config.retryCount) {
    config.retryCount = 0;
  }

  return config;
}, error => {
  console.error('请求拦截器错误:', error);
  return Promise.reject(error);
})

// 添加响应拦截器
axios.interceptors.response.use(response => {
  console.log('收到响应:', response.config.method.toUpperCase(), response.config.url, response.status);
  return response;
}, error => {
  console.error('响应拦截器错误:', error);
  if (error.response) {
    console.error('错误状态码:', error.response.status);
    console.error('错误数据:', error.response.data);
  }

  // 实现请求重试功能
  const config = error.config;
  // 如果配置了重试，并且没有超过重试次数
  if (config && config.retry && config.retryCount < config.retry) {
    // 重试计数器加1
    config.retryCount = config.retryCount || 0;
    config.retryCount++;

    console.log(`请求失败，正在进行第${config.retryCount}次重试...`);

    // 创建新的Promise来处理重试
    return new Promise(resolve => {
      // 延迟一段时间再重试
      setTimeout(() => {
        console.log(`重试请求: ${config.url}`);
        resolve(axios(config));
      }, config.retryDelay || 1000);
    });
  }

  return Promise.reject(error);
})


// 全局路由守卫
router.beforeEach((to, from, next) => {
  console.log('from:'+from.path)
  console.log('to:'+to.path)
  if (!/^\/auth/.test(to.path)) {
    // 优先检查token_admin，如果不存在则检查token
    if (!localStorage.getItem('token_admin') && !localStorage.getItem('token')) {
      console.warn('未检测到有效的管理员登录令牌，重定向到登录页面')
      router.push('/auth/login')
    } else {
      next()
    }
  } else {
    next()
  }

})

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
