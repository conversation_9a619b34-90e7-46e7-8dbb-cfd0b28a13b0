<?php
// 危险函数示例
function dangerous_exec_example($command) {
    // 直接执行用户输入的命令，存在命令注入风险
    $output = exec($command);
    return $output;
}

// SQL 注入示例
function sql_injection_example() {
    $user_id = $_GET['user_id']; // 未过滤的用户输入
    $query = "SELECT * FROM users WHERE id = " . $user_id;
    $result = mysql_query($query); // 使用危险的 mysql_query 函数
    return $result;
}

// 文件操作风险示例
function file_operation_example() {
    $filename = $_POST['filename']; // 未验证的用户输入
    $content = file_get_contents($filename); // 可能导致任意文件读取
    return $content;
}

// 反序列化漏洞示例
function unserialize_example() {
    $data = $_COOKIE['user_data']; // 来自 cookie 的数据
    $obj = unserialize($data); // 不安全的反序列化
    return $obj;
}

// XSS 漏洞示例
function xss_example() {
    $name = $_GET['name']; // 未过滤的用户输入
    echo "Welcome, " . $name; // 直接输出用户输入，可能导致 XSS
}
?>
