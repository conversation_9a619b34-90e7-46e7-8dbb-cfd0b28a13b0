<template>
  <div class="knowledge-points">
    <el-breadcrumb separator="/">
      <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>数据分析</el-breadcrumb-item>
      <el-breadcrumb-item>知识点管理</el-breadcrumb-item>
    </el-breadcrumb>

    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>知识点管理</span>
        <el-button style="float: right;" type="primary" size="small" @click="showAddDialog">添加知识点</el-button>
      </div>

      <!-- 知识点列表 -->
      <el-table :data="knowledgePoints" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="name" label="知识点名称"></el-table-column>
        <el-table-column prop="description" label="描述" show-overflow-tooltip></el-table-column>
        <el-table-column prop="module.name" label="所属模块"></el-table-column>
        <el-table-column label="操作" width="200">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </div>
    </el-card>

    <!-- 添加/编辑知识点对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="500px">
      <el-form :model="form" :rules="rules" ref="form" label-width="100px">
        <el-form-item label="知识点名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入知识点名称"></el-input>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input type="textarea" v-model="form.description" placeholder="请输入知识点描述" :rows="4"></el-input>
        </el-form-item>
        <el-form-item label="所属模块" prop="module_id">
          <el-select v-model="form.module_id" placeholder="请选择所属模块" style="width: 100%">
            <el-option
              v-for="item in modules"
              :key="item.id"
              :label="item.value"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'KnowledgePoints',
  data() {
    return {
      loading: false,
      knowledgePoints: [],
      modules: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      dialogVisible: false,
      dialogTitle: '添加知识点',
      form: {
        id: null,
        name: '',
        description: '',
        module_id: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入知识点名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        module_id: [
          { required: true, message: '请选择所属模块', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    fetchKnowledgePoints() {
      this.loading = true;
      console.log('获取知识点列表...');

      this.$axios.get('data_analysis/knowledge_points').then(res => {
        console.log('知识点数据响应状态:', res.status);
        console.log('知识点数据响应:', res.data);

        if (res.data.code === 200 || res.data.code === 0) {
          this.knowledgePoints = res.data.data || [];
          this.total = this.knowledgePoints.length;
          console.log('获取到知识点数量:', this.knowledgePoints.length);

          // 如果没有知识点，显示提示
          if (this.knowledgePoints.length === 0) {
            this.$message.info('暂无知识点数据');
          }
        } else {
          console.error('获取知识点失败:', res.data.message);
          this.$message.error('获取知识点列表失败: ' + (res.data.message || '未知错误'));

          // 保持使用模拟数据
          console.log('使用模拟数据显示');
        }
      }).catch(err => {
        console.error('获取知识点出错:', err);
        console.error('错误详情:', err.response ? err.response.data : '无响应数据');
        this.$message.error('获取知识点列表出错: ' + err.message);

        // 保持使用模拟数据
        console.log('使用模拟数据显示');
      }).finally(() => {
        this.loading = false;
      });
    },
    fetchModules() {
      console.log('获取模块列表...');

      this.$axios.get('api/modules').then(res => {
        console.log('模块数据响应状态:', res.status);
        console.log('模块数据响应:', res.data);

        if (res.data.code === 200 || res.data.code === 0) {
          this.modules = res.data.data || [];
          console.log('获取到模块数量:', this.modules.length);

          // 如果没有模块，显示提示
          if (this.modules.length === 0) {
            this.$message.warning('暂无模块数据，请先添加模块');
          }
        } else {
          console.error('获取模块失败:', res.data.message);
          this.$message.error('获取模块列表失败: ' + (res.data.message || '未知错误'));

          // 保持使用模拟数据
          console.log('使用模拟数据显示');
        }
      }).catch(err => {
        console.error('获取模块出错:', err);
        console.error('错误详情:', err.response ? err.response.data : '无响应数据');
        this.$message.error('获取模块列表出错: ' + err.message);

        // 保持使用模拟数据
        console.log('使用模拟数据显示');
      });
    },
    showAddDialog() {
      this.dialogTitle = '添加知识点';
      this.form = {
        id: null,
        name: '',
        description: '',
        module_id: ''
      };
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },
    handleEdit(row) {
      this.dialogTitle = '编辑知识点';
      // 确保module存在且有id
      const moduleId = row.module && row.module.id ? row.module.id : '';
      console.log(`编辑知识点: ID=${row.id}, 名称=${row.name}, 模块ID=${moduleId}`);

      this.form = {
        id: row.id,
        name: row.name,
        description: row.description || '',
        module_id: moduleId
      };
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },
    handleDelete(row) {
      this.$confirm(`确认删除知识点"${row.name}"吗？此操作将同时删除与该知识点相关的所有学习进度记录和课程关联。`, '警告', {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        console.log(`尝试删除知识点ID: ${row.id}, 名称: ${row.name}`);
        this.$message.info("正在删除知识点，请稍候...");

        this.$axios.delete(`data_analysis/knowledge_points/${row.id}`).then(res => {
          console.log("删除知识点响应:", res.data);
          if (res.data.code === 0) {
            this.$message.success(res.data.message || '删除成功');
            this.fetchKnowledgePoints();
          } else {
            console.error("删除知识点失败:", res.data.message);
            this.$message.error(res.data.message || '删除失败，请查看控制台日志');
          }
        }).catch(error => {
          console.error("删除知识点出错:", error);
          this.$message.error("删除知识点失败！请查看控制台日志");
        });
      }).catch(() => {
        // 取消删除
        this.$message.info('已取消删除');
      });
    },
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 确保module_id是数字类型
          if (this.form.module_id) {
            this.form.module_id = parseInt(this.form.module_id);
          }

          console.log('表单数据:', this.form);

          if (this.form.id) {
            // 编辑
            console.log(`尝试更新知识点ID: ${this.form.id}`);
            this.$message.info("正在更新知识点，请稍候...");

            // 打印完整的请求信息
            console.log(`PUT请求URL: data_analysis/knowledge_points/${this.form.id}`);
            console.log('请求头:', {
              'Content-Type': 'application/json',
              'Authorization': localStorage.getItem('token_admin') ? `Bearer ${localStorage.getItem('token_admin')}` : undefined
            });
            console.log('请求体:', JSON.stringify(this.form));

            this.$axios.put(`data_analysis/knowledge_points/${this.form.id}`, this.form).then(res => {
              console.log("更新知识点响应状态:", res.status);
              console.log("更新知识点响应头:", res.headers);
              console.log("更新知识点响应数据:", res.data);

              if (res.data.code === 200 || res.data.code === 0) {
                this.$message.success('更新成功');
                this.dialogVisible = false;
                this.fetchKnowledgePoints();
              } else {
                console.error("更新知识点失败:", res.data.message);
                this.$message.error(res.data.message || '更新失败，请查看控制台日志');
              }
            }).catch(error => {
              console.error("更新知识点出错:", error);
              console.error("错误详情:", error.response ? error.response.data : '无响应数据');
              this.$message.error("更新知识点失败！请查看控制台日志");
            });
          } else {
            // 添加
            console.log('尝试添加新知识点');
            this.$message.info("正在添加知识点，请稍候...");

            // 打印完整的请求信息
            console.log('POST请求URL: data_analysis/knowledge_points');
            console.log('请求头:', {
              'Content-Type': 'application/json',
              'Authorization': localStorage.getItem('token_admin') ? `Bearer ${localStorage.getItem('token_admin')}` : undefined
            });
            console.log('请求体:', JSON.stringify(this.form));

            this.$axios.post('data_analysis/knowledge_points', this.form).then(res => {
              console.log("添加知识点响应状态:", res.status);
              console.log("添加知识点响应头:", res.headers);
              console.log("添加知识点响应数据:", res.data);

              if (res.data.code === 200 || res.data.code === 0) {
                this.$message.success('添加成功');
                this.dialogVisible = false;
                this.fetchKnowledgePoints();
              } else {
                console.error("添加知识点失败:", res.data.message);
                this.$message.error(res.data.message || '添加失败，请查看控制台日志');
              }
            }).catch(error => {
              console.error("添加知识点出错:", error);
              console.error("错误详情:", error.response ? error.response.data : '无响应数据');
              this.$message.error("添加知识点失败！请查看控制台日志");
            });
          }
        } else {
          console.warn('表单验证失败');
          return false;
        }
      });
    },
    handleSizeChange(val) {
      this.pageSize = val;
    },
    handleCurrentChange(val) {
      this.currentPage = val;
    }
  },
  mounted() {
    console.log('KnowledgePoints组件挂载');

    // 检查是否已登录
    if (!localStorage.getItem('token_admin')) {
      console.warn('管理员未登录，无法获取数据');
      this.$message.warning('请先登录');
      this.$router.push('/auth/login');
      return;
    }

    // 使用模拟数据
    this.modules = [
      { id: 1, value: "Python编程" },
      { id: 2, value: "Web开发" },
      { id: 3, value: "数据库" },
      { id: 4, value: "数据结构" },
      { id: 5, value: "算法" },
      { id: 6, value: "机器学习" },
      { id: 7, value: "网络安全" },
      { id: 8, value: "Java开发" }
    ];

    this.knowledgePoints = [
      { id: 1, name: "变量与数据类型", description: "Python中的变量定义和基本数据类型", module: { id: 1, name: "Python编程" }, created_at: "2023-01-01" },
      { id: 2, name: "条件语句", description: "if-else条件判断和逻辑运算", module: { id: 1, name: "Python编程" }, created_at: "2023-01-02" },
      { id: 3, name: "循环结构", description: "for和while循环的使用方法", module: { id: 1, name: "Python编程" }, created_at: "2023-01-03" },
      { id: 4, name: "函数与方法", description: "函数定义、参数传递和返回值", module: { id: 1, name: "Python编程" }, created_at: "2023-01-04" },
      { id: 5, name: "HTML基础", description: "HTML标签和文档结构", module: { id: 2, name: "Web开发" }, created_at: "2023-01-05" },
      { id: 6, name: "CSS样式", description: "CSS选择器和样式属性", module: { id: 2, name: "Web开发" }, created_at: "2023-01-06" },
      { id: 7, name: "JavaScript", description: "JavaScript语法和DOM操作", module: { id: 2, name: "Web开发" }, created_at: "2023-01-07" },
      { id: 8, name: "SQL基础", description: "SQL查询语言基础", module: { id: 3, name: "数据库" }, created_at: "2023-01-08" }
    ];

    this.total = this.knowledgePoints.length;
    this.loading = false;

    // 显示提示
    // this.$message.warning('使用模拟数据显示');

    // 尝试从后端获取数据
    this.fetchKnowledgePoints();
    this.fetchModules();
  }
}
</script>

<style scoped>
.knowledge-points {
  padding: 20px;
}
.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
