import os

APP_ENV = "testing"

base_url = os.path.abspath(os.path.dirname(__file__))

# 数据库连接配置
# DB_HOST = 'localhost'
# DB_PORT = 3306
# DB_USER = 'root'
# DB_PASSWORD = '123456'
# DB_NAME = 'GenAIPTS'

class Config:
    # 数据库连接参数
    DB_HOST = 'localhost'
    DB_PORT = 3306
    DB_USER = 'root'
    DB_PASSWORD = '123456'
    DB_NAME = 'GenAIPTS'

    DEBUG = True
    TESTING = False
    SQLALCHEMY_DATABASE_URI = f'mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SECRET_KEY = 'hello'
    UPLOAD_FOLDER = os.path.join(base_url, 'uploads')

import logging
from redis import StrictRedis

class BaseConfig:
    # 数据库连接参数
    DB_HOST = 'localhost'
    DB_PORT = 3306
    DB_USER = 'root'
    DB_PASSWORD = '123456'
    DB_NAME = 'GenAIPTS'

    SQLALCHEMY_DATABASE_URI = f'mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}'

    SQLALCHEMY_COMMIT_ON_TEARDOWN = True
    #是否开启跟踪
    SQLALCHEMY_TRACK_MODIFICATIONS = True
    SECRET_KEY = "hello"
    '''
    根据业务和场景添加其他相关配置
    '''

    #配置Redis数据库
    REDIS_HOST = '127.0.0.1'
    REDIS_PORT = 6379
    REDIS_DB = 1
   # 配置session数据存储到redis数据库
    SESSION_TYPE = 'redis'
    # 指定存储session数据的redis的位置
    SESSION_REDIS = StrictRedis(host=REDIS_HOST, port=REDIS_PORT,db=REDIS_DB)
    # 开启session数据的签名，意思是让session数据不以明文形式存储
    SESSION_USE_SIGNER = True
    # 設置session的会话的超时时长 ：一天,全局指定
    PERMANENT_SESSION_LIFETIME = 3600 * 24

    #QQ邮箱配置
    MAIL_DEBUG = True             # 开启debug，便于调试看信息
    MAIL_SUPPRESS_SEND = False    # 发送邮件，为True则不发送
    MAIL_SERVER = 'smtp.qq.com'   # 邮箱服务器
    MAIL_PORT = 465               # 端口
    MAIL_USE_SSL = True           # 重要，qq邮箱需要使用SSL
    MAIL_USE_TLS = False          # 不需要使用TLS
    MAIL_USERNAME = '<EMAIL>'  # 填邮箱
    MAIL_PASSWORD = 'pmbdjzlrbdcwecaj'  # 替换为新的授权码
    FLASK_MAIL_SENDER = 'GenAIPTS<<EMAIL>>'   #邮件发送方
    FLASK_MAIL_SUBJECT_PREFIX = '[GenAIPTS]'     #邮件标题
    #MAIL_DEFAULT_SENDER = '<EMAIL>'  # 填邮箱，默认发送者

class TestingConfig(BaseConfig):
    DEBUG = True
    LOGGING_LEVEL = logging.DEBUG
    # SQLALCHEMY_DATABASE_URI = "mysql+pymysql://root:123456@localhost:3306/test"
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    UPLOAD_FOLDER = os.path.join(base_url, 'uploads')

class DevelopmentConfig(BaseConfig):
    DEBUG = False
    LOGGING_LEVEL = logging.WARNING
    # SQLALCHEMY_DATABASE_URI = "mysql+pymysql://root:123456@localhost:3306/test"
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    UPLOAD_FOLDER = os.path.join(base_url, 'uploads')

config = {
    "testing": TestingConfig,
    "devolopment": DevelopmentConfig,
}