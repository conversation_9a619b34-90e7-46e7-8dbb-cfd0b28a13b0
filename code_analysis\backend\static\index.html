<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CodeAnalysis</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cinzel+Decorative:wght@700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.min.js"></script>
    <link href="/static/styles.css" rel="stylesheet">
    <style>
        :root {
            --bg-color: #ffffff;
            --text-color: #212529;
            --card-bg: #ffffff;
            --border-color: #dee2e6;
            --custom-file-bg: #f8f9fa;
            --custom-file-border: #ddd;
            --custom-file-hover-bg: #f1f8ff;
            --custom-file-hover-border: #0d6efd;
            --pre-bg: #f8f9fa;
            --pre-color: #212529;
        }

        [data-theme="dark"] {
            --bg-color: #212529;
            --text-color: #f8f9fa;
            --card-bg: #343a40;
            --border-color: #495057;
            --custom-file-bg: #2b3035;
            --custom-file-border: #495057;
            --custom-file-hover-bg: #3d4247;
            --custom-file-hover-border: #0d6efd;
            --pre-bg: #2b3035;
            --pre-color: #f8f9fa;
        }

        body {
            background-color: var(--bg-color);
            color: var(--text-color);
            transition: all 0.3s ease;
        }

        .card {
            background-color: var(--card-bg);
            border-color: var(--border-color);
        }

        .card-header {
            background-color: var(--card-bg);
            border-bottom-color: var(--border-color);
        }

        .container { max-width: 1200px; margin-top: 2rem; }
        .result-card {
            margin: 1rem 0;
            padding: 1rem;
            border-radius: 8px;
            background-color: var(--card-bg);
            border: 1px solid var(--border-color);
        }
        .loading { text-align: center; margin: 2rem 0; }
        .file-list { margin-top: 1rem; }
        .file-list-item { padding: 0.5rem; border-bottom: 1px solid #eee; }
        .file-list-item:last-child { border-bottom: none; }
        pre {
            white-space: pre-wrap;
            word-wrap: break-word;
            max-height: 400px;
            overflow-y: auto;
            background-color: var(--pre-bg) !important;
            color: var(--pre-color) !important;
            border: 1px solid var(--border-color);
            padding: 1rem;
        }
        .btn-link { text-decoration: none; }
        .collapse { transition: all 0.3s ease; }
        .upload-container {
            position: relative;
            min-height: 100px;
            margin-bottom: 1rem;
        }
        .upload-section {
            width: 100%;
            transition: all 0.3s ease;
        }
        .upload-section input[type="file"] {
            display: block !important;
            opacity: 1 !important;
            position: relative !important;
            width: 100%;
        }
        .custom-file-upload {
            border: 2px dashed var(--custom-file-border);
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            background: var(--custom-file-bg);
            transition: all 0.3s ease;
        }
        .custom-file-upload:hover {
            border-color: var(--custom-file-hover-border);
            background: var(--custom-file-hover-bg);
        }
        .form-label {
            margin-bottom: 10px;
            color: #666;
        }
        .file-list {
            margin-top: 1rem;
            max-height: 200px;
            overflow-y: auto;
        }
        .file-list-item {
            padding: 8px;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .file-list-item:last-child {
            border-bottom: none;
        }
        .progress {
            background-color: #e9ecef;
            border-radius: 0.25rem;
            box-shadow: inset 0 1px 2px rgba(0,0,0,.1);
        }

        .progress-bar {
            display: flex;
            flex-direction: column;
            justify-content: center;
            color: #fff;
            text-align: center;
            background-color: #007bff;
            transition: width .6s ease;
        }

        .progress-bar-striped {
            background-image: linear-gradient(45deg,rgba(255,255,255,.15) 25%,transparent 25%,transparent 50%,rgba(255,255,255,.15) 50%,rgba(255,255,255,.15) 75%,transparent 75%,transparent);
            background-size: 1rem 1rem;
        }

        .progress-bar-animated {
            animation: progress-bar-stripes 1s linear infinite;
        }

        @keyframes progress-bar-stripes {
            from { background-position: 1rem 0; }
            to { background-position: 0 0; }
        }

        /* 主题切换按钮样式 */
        .theme-toggle {
            position: fixed;
            top: 1rem;
            right: 1rem;
            padding: 0.5rem;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            background-color: var(--card-bg);
            border: 1px solid var(--border-color);
            color: var(--text-color);
            transition: all 0.3s ease;
        }

        .theme-toggle:hover {
            background-color: var(--custom-file-hover-bg);
        }

        .vulnerability-item {
            background-color: var(--card-bg);
            border-color: var(--border-color) !important;
        }

        .list-group-item {
            background-color: var(--card-bg);
            border-color: var(--border-color);
            color: var(--text-color);
        }

        .text-muted {
            color: #6c757d !important;
        }

        [data-theme="dark"] .text-muted {
            color: #adb5bd !important;
        }

        /* 功能卡片样式 */
        .feature-card {
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        /* 智能辅导页面样式 */
        .tutoring-container {
            display: flex;
            flex-direction: column;
            height: 600px;
        }

        .messages-container {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
            background-color: var(--bg-color);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .message {
            display: flex;
            margin-bottom: 15px;
            max-width: 80%;
        }

        .user-message {
            margin-left: auto;
            flex-direction: row-reverse;
        }

        .assistant-message {
            margin-right: auto;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            flex-shrink: 0;
        }

        .user-message .message-avatar {
            background-color: #4a6cf7;
            color: white;
        }

        .assistant-message .message-avatar {
            background-color: #6c757d;
            color: white;
        }

        .message-content {
            background-color: var(--card-bg);
            padding: 12px 15px;
            border-radius: 10px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .user-message .message-content {
            background-color: #4a6cf7;
            color: white;
            border-top-right-radius: 0;
        }

        .assistant-message .message-content {
            background-color: var(--card-bg);
            border-top-left-radius: 0;
        }

        .message-timestamp {
            font-size: 12px;
            color: #adb5bd;
            margin-top: 5px;
            text-align: right;
        }

        .user-message .message-timestamp {
            color: rgba(255, 255, 255, 0.7);
        }

        .input-container {
            padding: 10px;
            background-color: var(--card-bg);
            border-radius: 8px;
        }

        #question-input {
            resize: none;
        }

        .thinking {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 10px;
            color: var(--text-color);
        }

        .thinking .spinner {
            margin-right: 10px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .title-container {
            text-align: center;
            margin-bottom: 2rem;
            padding: 2rem 0;
        }

        .main-title {
            font-family: 'Cinzel Decorative', cursive;
            font-size: 3rem;
            background: linear-gradient(45deg, #1a1a1a, #4a4a4a);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        [data-theme="dark"] .main-title {
            background: linear-gradient(45deg, #ffffff, #cccccc);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .subtitle {
            font-family: "Microsoft YaHei", sans-serif;
            color: var(--text-color);
            font-size: 1.2rem;
            opacity: 0.8;
        }

        /* JSON格式化样式 */
        .formatted-json {
            font-family: monospace;
            white-space: pre-wrap;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
        }
        .json-key {
            color: #0066cc;
        }
        .json-string {
            color: #008800;
        }
        .json-number {
            color: #aa0000;
        }
        .json-boolean {
            color: #0000ff;
        }
        .json-null {
            color: #880088;
        }
    </style>
</head>
<body>
    <button class="theme-toggle" onclick="toggleTheme()" title="切换主题" style="display: none;">
        <i class="bi bi-moon-fill" id="themeIcon"></i>
    </button>
    <div class="container">
        <!-- <div class="title-container">
            <h1 class="main-title">CodeAnalysis</h1>
            <div class="subtitle">代码分析工具</div>
        </div> -->

        <!-- API配置部分 -->
        <div class="card mb-4" style="display: none;">
            <div class="card-header">
                <h5 class="mb-0">API配置</h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-4">
                        <input type="text" id="apiKey" class="form-control" placeholder="OpenAI API Key">
                    </div>
                    <div class="col-md-4">
                        <input type="text" id="apiBase" class="form-control" placeholder="API Base URL（可选）">
                    </div>
                    <div class="col-md-2">
                        <select id="modelSelect" class="form-select">
                            <option value="">选择模型</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button onclick="updateConfig()" class="btn btn-primary w-100">更新配置</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 导航栏 -->
        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">功能选择</h5>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="card mb-3 feature-card" onclick="switchToFeature('syntaxCheck')">
                            <div class="card-body text-center">
                                <i class="bi bi-code-slash fs-1 mb-3 text-warning"></i>
                                <h5>语法检测</h5>
                                <p class="text-muted">检测代码中的语法错误和基础问题</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card mb-3 feature-card" onclick="switchToFeature('codeAnalysis')">
                            <div class="card-body text-center">
                                <i class="bi bi-shield-check fs-1 mb-3 text-primary"></i>
                                <h5>代码分析</h5>
                                <p class="text-muted">分析代码质量、结构和潜在问题</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card mb-3 feature-card" onclick="switchToFeature('tutoring')">
                            <div class="card-body text-center">
                                <i class="bi bi-chat-dots fs-1 mb-3 text-success"></i>
                                <h5>智能辅导</h5>
                                <p class="text-muted">获取编程问题的即时解答和指导</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 语法检测部分 -->
        <div class="card mb-4" id="syntaxCheckSection" style="display: none;">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">语法检测</h5>
                    <button class="btn btn-sm btn-outline-secondary" onclick="switchToFeature('codeAnalysis')">
                        <i class="bi bi-arrow-left"></i> 返回
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="mb-0">上传文件检测</h6>
                            </div>
                            <div class="card-body">
                                <div class="custom-file-upload mb-3">
                                    <label for="syntaxFile" class="form-label">选择文件 (.php, .java, .js, .py)</label>
                                    <input type="file" id="syntaxFile" class="form-control" accept=".php,.java,.js,.py">
                                </div>
                                <button onclick="checkSyntaxFile()" id="syntaxFileBtn" class="btn btn-warning" disabled>
                                    检测语法
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="mb-0">粘贴代码检测</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="syntaxLanguage" class="form-label">选择语言</label>
                                    <select class="form-control" id="syntaxLanguage">
                                        <option value="python">Python</option>
                                        <option value="javascript">JavaScript</option>
                                        <option value="php">PHP</option>
                                        <option value="java">Java</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="codeInput" class="form-label">代码输入</label>
                                    <textarea id="codeInput" class="form-control" rows="8" placeholder="在此粘贴代码..."></textarea>
                                </div>
                                <button onclick="checkSyntaxCode()" id="syntaxCodeBtn" class="btn btn-warning">
                                    检测语法
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 语法检测结果 -->
                <div id="syntaxResults" class="mt-4" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">语法检测结果</h5>
                        </div>
                        <div class="card-body">
                            <div id="syntaxStatus" class="alert mb-3"></div>
                            <div id="syntaxErrorList" style="display: none;">
                                <h6>错误详情:</h6>
                                <div class="list-group" id="errorItems">
                                    <!-- 错误项将动态添加到这里 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 文件上传部分 -->
        <div class="card mb-4" id="codeAnalysisSection">
            <div class="card-header">
                <h5 class="mb-0">代码分析</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <select id="uploadType" class="form-select mb-3">
                        <option value="single">单文件分析</option>
                        <option value="folder">项目文件夹分析</option>
                    </select>
                </div>

                <div class="mb-3">
                    <label for="projectType" class="form-label">项目类型</label>
                    <select class="form-control" id="projectType">
                        <option value="auto">自动检测</option>
                        <option value="php">PHP项目</option>
                        <option value="python">Python项目</option>
                        <option value="java">Java项目</option>
                        <option value="javascript">JavaScript项目</option>
                    </select>
                </div>

                <div class="upload-container">
                    <!-- 单文件上传 -->
                    <div id="singleFileUpload" class="upload-section">
                        <div class="custom-file-upload">
                            <label for="codeFile" class="form-label">选择文件 (.php, .java, .js, .py)</label>
                            <input type="file" id="codeFile" class="form-control" accept=".php,.java,.js,.py">
                        </div>
                        <div id="singleFileList" class="file-list mt-2"></div>
                    </div>

                    <!-- 文件夹上传 -->
                    <div id="folderUpload" class="upload-section" style="display: none;">
                        <div class="custom-file-upload">
                            <label for="codeFolder" class="form-label">选择项目文件夹</label>
                            <input type="file" id="codeFolder" class="form-control" webkitdirectory directory>
                        </div>
                        <div id="folderFileList" class="file-list mt-2"></div>
                    </div>
                </div>

                <button onclick="startAudit()" id="auditBtn" class="btn btn-success mt-3" disabled>
                    开始分析
                </button>
            </div>
        </div>

        <!-- 结果显示部分 -->
        <div id="results" style="display: none;">
            <h2>代码分析结果</h2>
            <div class="accordion" id="auditResults">
                <!-- 结果将动态添加到这里 -->
            </div>
        </div>

        <!-- 加载提示 -->
        <div id="loading" class="loading" style="display: none;">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">分析中...</span>
            </div>
            <div class="mt-3">
                <p id="loadingText" class="mb-2">代码分析中，请稍候...</p>
                <div class="progress" style="height: 20px; width: 300px; margin: 0 auto;">
                    <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated"
                         role="progressbar" style="width: 0%;"
                         aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                </div>
                <p id="currentFile" class="mt-2 text-muted small"></p>
            </div>
        </div>

        <!-- 智能辅导页面 -->
        <div id="tutoringSection" class="card mb-4" style="display: none;">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">智能辅导</h5>
                    <button class="btn btn-sm btn-outline-secondary" onclick="switchToFeature('codeAnalysis')">
                        <i class="bi bi-arrow-left"></i> 返回
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="tutoring-container">
                    <div class="tutoring-info mb-3">
                        <div id="topic-badge" class="badge bg-info mb-2" style="display: none;">主题: <span id="topic-text"></span></div>
                        <div id="difficulty-badge" class="badge bg-warning mb-2 ms-2" style="display: none;">难度: <span id="difficulty-text"></span></div>
                    </div>

                    <div class="messages-container" id="messages-container">
                        <!-- 消息将在这里动态添加 -->
                    </div>

                    <div class="input-container mt-3">
                        <div class="input-group">
                            <textarea id="question-input" class="form-control" placeholder="输入你的编程问题..." rows="3"></textarea>
                            <button id="send-button" class="btn btn-primary">
                                <i class="bi bi-send"></i>
                            </button>
                        </div>
                        <div class="form-text text-muted mt-2">
                            提示: 你可以提问关于编程语言、算法、调试问题等任何编程相关的问题。
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const apiUrl = 'http://127.0.0.1:8000';

        // 获取可用模型列表
        async function fetchAvailableModels() {
            try {
                const response = await fetch(`${apiUrl}/api/models`);
                if (response.ok) {
                    const data = await response.json();
                    console.log('获取到的模型数据:', data);

                    const modelSelect = document.getElementById('modelSelect');
                    modelSelect.innerHTML = '<option value="">选择模型</option>';

                    if (data.models && typeof data.models === 'object') {
                        // 遍历每个模型类别
                        Object.entries(data.models).forEach(([category, models]) => {
                            if (models.length > 0) {
                                const optgroup = document.createElement('optgroup');
                                optgroup.label = category;

                                // 对模型进行排序
                                const sortedModels = [...models].sort((a, b) => {
                                    // 将Pro模型排在后面
                                    const aIsPro = a.startsWith('Pro/');
                                    const bIsPro = b.startsWith('Pro/');
                                    if (aIsPro && !bIsPro) return 1;
                                    if (!aIsPro && bIsPro) return -1;
                                    return a.localeCompare(b);
                                });

                                sortedModels.forEach(model => {
                                    const option = document.createElement('option');
                                    option.value = model;
                                    // 美化显示名称
                                    option.textContent = model.split('/').pop() || model;
                                    if (model === data.current_model) {
                                        option.selected = true;
                                    }
                                    optgroup.appendChild(option);
                                });

                                modelSelect.appendChild(optgroup);
                            }
                        });

                        // 如果没有选中的模型，默认选择第一个GPT模型
                        if (!modelSelect.value && data.models.GPT?.length > 0) {
                            modelSelect.value = data.models.GPT[0];
                        }
                    } else {
                        console.error('模型数据格式错误:', data);
                    }
                } else {
                    const error = await response.json();
                    console.error('获取模型列表失败:', error);
                }
            } catch (error) {
                console.error('获取模型列表失败:', error);
            }
        }

        // 更新配置函数
        async function updateConfig() {
            const apiKey = document.getElementById('apiKey').value;
            let apiBase = document.getElementById('apiBase').value;
            const modelSelect = document.getElementById('modelSelect');
            const model = modelSelect.value || modelSelect.options[1]?.value; // 如果没有选择，使用第一个有效选项

            if (!apiKey) {
                alert('请输入 API Key');
                return;
            }

            // 规范化 API 基础 URL
            if (apiBase) {
                apiBase = apiBase.trim();
                if (!apiBase.startsWith('http')) {
                    apiBase = 'https://' + apiBase;
                }
                if (!apiBase.endsWith('/v1')) {
                    apiBase = apiBase.replace(/\/+$/, '') + '/v1';
                }
            }

            try {
                console.log('发送配置:', { api_key: apiKey, api_base: apiBase, model });  // 添加调试日志

                const response = await fetch(`${apiUrl}/api/configure`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        api_key: apiKey,
                        api_base: apiBase || null,
                        model: model || null
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    console.log('配置更新结果:', result);  // 添加调试日志
                    alert(result.message);
                    await fetchAvailableModels();
                } else {
                    const error = await response.json();
                    throw new Error(error.detail || '配置更新失败');
                }
            } catch (error) {
                console.error('配置更新失败:', error);
                alert('配置更新失败: ' + error.message);
            }
        }

        // 初始化上传类型切换
        function initializeUploadTypes() {
            const uploadType = document.getElementById('uploadType');
            const sections = {
                single: document.getElementById('singleFileUpload'),
                folder: document.getElementById('folderUpload')
            };

            // 确保初始状态正确
            sections.single.style.display = 'block';
            sections.folder.style.display = 'none';

            uploadType.addEventListener('change', function() {
                // 使用简单的显示/隐藏切换
                Object.entries(sections).forEach(([type, section]) => {
                    section.style.display = type === this.value ? 'block' : 'none';

                    // 如果是隐藏的部分，清除其文件选择
                    if (type !== this.value) {
                        const input = section.querySelector('input[type="file"]');
                        if (input) {
                            input.value = '';
                        }
                        const fileList = section.querySelector('.file-list');
                        if (fileList) {
                            fileList.innerHTML = '';
                        }
                    }
                });

                // 更新按钮状态
                updateAuditButtonState();
            });
        }

        // 修改文件输入监听器
        function initializeFileInputs() {
            const fileInputs = {
                'codeFile': 'singleFileList',
                'codeFolder': 'folderFileList'
            };

            Object.entries(fileInputs).forEach(([inputId, listId]) => {
                const input = document.getElementById(inputId);
                if (input) {
                    input.addEventListener('change', function() {
                        if (this.files && this.files.length > 0) {
                            updateFileList(this.files, listId);
                            document.getElementById('auditBtn').disabled = false;
                        } else {
                            document.getElementById(listId).innerHTML = '';
                            document.getElementById('auditBtn').disabled = true;
                        }
                    });
                }
            });
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            initTheme();
            initializeUploadTypes();
            initializeFileInputs();
            fetchAvailableModels();
        });

        // API Base URL 改变时更新模型列表
        document.getElementById('apiBase').addEventListener('change', async (event) => {
            console.log('API Base URL changed:', event.target.value);
            await fetchAvailableModels();
        });

        // 更新文件列表显示
        function updateFileList(files, containerId) {
            const container = document.getElementById(containerId);
            container.innerHTML = '';

            if (containerId === 'folderFileList') {
                // 获取项目类型
                const projectType = document.getElementById('projectType').value;

                // 根据项目类型定义支持的文件类型
                const extensionMap = {
                    'php': ['.php'],
                    'python': ['.py', '.pyw'],
                    'java': ['.java', '.jsp'],
                    'javascript': ['.js', '.jsx', '.ts', '.tsx'],
                    'auto': ['.php', '.java', '.js', '.py', '.jsx', '.ts', '.tsx', '.pyw', '.jsp']
                };

                // 获取主要文件类型和辅助文件类型
                const primaryExtensions = extensionMap[projectType] || extensionMap.auto;
                const auxiliaryExtensions = ['.html', '.css', '.json', '.xml', '.yml', '.yaml'];

                // 分类文件
                const validFiles = Array.from(files).filter(file => {
                    const ext = '.' + file.name.split('.').pop().toLowerCase();
                    return primaryExtensions.includes(ext);
                });

                const auxiliaryFiles = Array.from(files).filter(file => {
                    const ext = '.' + file.name.split('.').pop().toLowerCase();
                    return auxiliaryExtensions.includes(ext);
                });

                // 计算跳过的文件数
                const skippedCount = files.length - validFiles.length - auxiliaryFiles.length;

                // 显示处理信息
                showProcessingInfo(validFiles, auxiliaryFiles, skippedCount);

                // 只有存在有效文件时才启用分析按钮
                document.getElementById('auditBtn').disabled = validFiles.length === 0;
            } else {
                // 对于单文件上传，保持原有逻辑
                Array.from(files).forEach(file => {
                    const item = document.createElement('div');
                    item.className = 'file-list-item';
                    item.innerHTML = `
                        <i class="bi bi-file-earmark-text"></i>
                        ${file.webkitRelativePath || file.name}
                        <small class="text-muted">(${formatFileSize(file.size)})</small>
                    `;
                    container.appendChild(item);
                });
            }
        }

        // 修改分析按钮状态更新函数
        function updateAuditButtonState() {
            const uploadType = document.getElementById('uploadType').value;
            const inputId = uploadType === 'single' ? 'codeFile' : 'codeFolder';
            const input = document.getElementById(inputId);

            document.getElementById('auditBtn').disabled = !input || !input.files.length;
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 修改分析函数
        async function startAudit() {
            showLoading();
            const uploadType = document.getElementById('uploadType').value;

            try {
                let result;
                if (uploadType === 'single') {
                    const file = document.getElementById('codeFile').files[0];
                    result = await auditSingleFile(file);
                } else {
                    const files = document.getElementById('codeFolder').files;
                    result = await auditFolder(files);
                }

                // 确保结果存在且格式正确
                if (!result || typeof result !== 'object') {
                    throw new Error('无效的响应数据');
                }

                // 显示结果
                displayAuditResults(result);
            } catch (error) {
                console.error('分析错误:', error);
                alert('分析失败: ' + error.message);
            } finally {
                hideLoading();
            }
        }

        async function auditSingleFile(file) {
            const formData = new FormData();
            formData.append('file', file);

            const apiKey = document.getElementById('apiKey').value;
            const apiBase = document.getElementById('apiBase').value;

            if (apiKey) formData.append('api_key', apiKey);
            if (apiBase) formData.append('api_base', apiBase);

            try {
                const response = await fetch(`${apiUrl}/api/audit`, {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error('分析请求失败');
                }

                const result = await response.json();
                return result;
            } catch (error) {
                throw new Error('分析失败: ' + error.message);
            }
        }

        // 修改 auditFolder 函数
        async function auditFolder(files) {
            try {
                const projectType = document.getElementById('projectType').value;

                // 根据项目类型定义支持的文件类型
                const extensionMap = {
                    'php': ['.php'],
                    'python': ['.py', '.pyw'],
                    'java': ['.java', '.jsp'],
                    'javascript': ['.js', '.jsx', '.ts', '.tsx'],
                    'auto': ['.php', '.java', '.js', '.py', '.jsx', '.ts', '.tsx', '.pyw', '.jsp']
                };

                // 获取主要文件类型和可选的辅助文件类型
                const primaryExtensions = extensionMap[projectType] || extensionMap.auto;
                const auxiliaryExtensions = ['.html', '.css', '.json', '.xml', '.yml', '.yaml'];

                // 分类文件
                const validFiles = Array.from(files).filter(file => {
                    const ext = '.' + file.name.split('.').pop().toLowerCase();
                    return primaryExtensions.includes(ext);
                });

                const auxiliaryFiles = Array.from(files).filter(file => {
                    const ext = '.' + file.name.split('.').pop().toLowerCase();
                    return auxiliaryExtensions.includes(ext);
                });

                if (validFiles.length === 0) {
                    throw new Error(`未找到支持的源代码文件（支持 ${primaryExtensions.join(', ')}）`);
                }

                // 显示初始进度
                updateProgress(0, validFiles.length, '准备文件...');

                // 创建ZIP文件
                const zip = new JSZip();

                // 添加主要源代码文件
                for (let i = 0; i < validFiles.length; i++) {
                    const file = validFiles[i];
                    const relativePath = file.webkitRelativePath || file.name;
                    zip.file(relativePath, file);
                    updateProgress(i + 1, validFiles.length, `正在处理: ${relativePath}`);
                }

                // 可选：添加辅助文件（配置文件等）
                if (auxiliaryFiles.length > 0) {
                    auxiliaryFiles.forEach(file => {
                        const relativePath = file.webkitRelativePath || file.name;
                        zip.file(relativePath, file);
                    });
                }

                // 显示处理信息，包括主要文件和辅助文件的统计
                showProcessingInfo(validFiles, auxiliaryFiles, files.length - validFiles.length - auxiliaryFiles.length);

                // 更新进度显示为压缩阶段
                updateProgress(validFiles.length, validFiles.length, '正在压缩文件...');
                const zipBlob = await zip.generateAsync({ type: 'blob' });

                // 准备上传
                const formData = new FormData();
                formData.append('project', new File([zipBlob], 'project.zip'));

                const apiKey = document.getElementById('apiKey').value;
                const apiBase = document.getElementById('apiBase').value;

                if (apiKey) formData.append('api_key', apiKey);
                if (apiBase) formData.append('api_base', apiBase);

                // 更新进度显示为分析阶段
                resetProgress();
                document.getElementById('loadingText').textContent = '正在进行代码分析...';
                document.getElementById('currentFile').textContent = '正在初始化分析...';

                const response = await fetch(`${apiUrl}/api/audit/project`, {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error('项目分析失败');
                }

                const result = await response.json();

                // 验证响应数据结构
                if (!result || typeof result !== 'object') {
                    throw new Error('无效的响应数据');
                }

                if (!result.status || !Array.isArray(result.suspicious_files)) {
                    throw new Error('响应数据格式错误');
                }

                return result;
            } catch (error) {
                throw new Error('分析失败: ' + error.message);
            }
        }

        // 添加重置进度条函数
        function resetProgress() {
            const progressBar = document.getElementById('progressBar');
            const currentFileText = document.getElementById('currentFile');

            progressBar.style.width = '0%';
            progressBar.setAttribute('aria-valuenow', 0);
            progressBar.textContent = '0%';
            currentFileText.textContent = '';
        }

        // 修改更新进度条函数
        function updateProgress(processed, total, message = '') {
            const percentage = Math.round((processed / total) * 100);
            const progressBar = document.getElementById('progressBar');
            const loadingText = document.getElementById('loadingText');
            const currentFileText = document.getElementById('currentFile');

            progressBar.style.width = `${percentage}%`;
            progressBar.setAttribute('aria-valuenow', percentage);
            progressBar.textContent = `${percentage}%`;

            if (message) {
                currentFileText.textContent = message;
            }

            loadingText.textContent = `处理进度：${processed}/${total}`;
        }

        // 更新显示处理信息的函数
        function showProcessingInfo(validFiles, auxiliaryFiles, skippedCount) {
            const container = document.getElementById('folderFileList');
            const projectType = document.getElementById('projectType').value;

            container.innerHTML = `
                <div class="alert alert-info">
                    <h6 class="mb-2">文件处理信息：</h6>
                    <p class="mb-1">待分析主要文件数：${validFiles.length}</p>
                    <p class="mb-1">相关辅助文件数：${auxiliaryFiles.length}</p>
                    <p class="mb-1">已跳过文件数：${skippedCount}</p>
                    <p class="mb-0">项目类型：${projectType === 'auto' ? '自动检测' : projectType.toUpperCase()}</p>
                </div>
                <div class="mt-3">
                    <h6>待分析主要文件：</h6>
                    ${validFiles.map(file => `
                        <div class="file-list-item">
                            <i class="bi bi-file-earmark-code"></i>
                            ${file.webkitRelativePath || file.name}
                            <small class="text-muted">(${formatFileSize(file.size)})</small>
                        </div>
                    `).join('')}
                </div>
                ${auxiliaryFiles.length > 0 ? `
                    <div class="mt-3">
                        <h6>相关辅助文件：</h6>
                        ${auxiliaryFiles.map(file => `
                            <div class="file-list-item text-muted">
                                <i class="bi bi-file-earmark"></i>
                                ${file.webkitRelativePath || file.name}
                                <small>(${formatFileSize(file.size)})</small>
                            </div>
                        `).join('')}
                    </div>
                ` : ''}
            `;
        }

        function displayAuditResults(result) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.style.display = 'block';
            resultsDiv.innerHTML = '';

            // 记录原始结果以便调试
            console.log('原始分析结果:', result);
            console.log('分析结果类型:', typeof result);
            console.log('分析结果状态:', result.status);
            console.log('可疑文件数组:', result.suspicious_files);
            
            if (result.suspicious_files && result.suspicious_files.length > 0) {
                console.log('第一个可疑文件详情:', result.suspicious_files[0]);
                if (result.suspicious_files[0].issues) {
                    console.log('第一个文件的issues类型:', typeof result.suspicious_files[0].issues);
                    console.log('第一个文件的issues内容:', result.suspicious_files[0].issues);
                }
            }

            // 确保结果对象包含必要的字段
            if (!result || result.status !== 'success') {
                resultsDiv.innerHTML = '<div class="alert alert-danger">无效的分析结果数据</div>';
                return;
            }

            // 使用 summary 字段，如果不存在则尝试使用 report.summary
            const summary = result.summary || (result.report ? result.report.summary : null);
            if (!summary) {
                resultsDiv.innerHTML = '<div class="alert alert-danger">分析结果缺少摘要信息</div>';
                return;
            }

            // 创建摘要卡片
            const summaryCard = document.createElement('div');
            summaryCard.className = 'card mb-4';

            // 确定风险等级和颜色
            const riskLevel = summary.risk_level || 'info';
            const riskLevelColor = riskLevel === 'high' ? 'danger' : 
                                  riskLevel === 'medium' ? 'warning' : 'info';

            summaryCard.innerHTML = `
                <div class="card-header bg-${riskLevelColor} text-white">
                    <h5 class="mb-0">分析摘要</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><i class="bi bi-check-circle-fill text-success"></i> 状态: ${result.status}</p>
                            ${result.message ? `<p><i class="bi bi-info-circle-fill"></i> 消息: ${result.message}</p>` : ''}
                        </div>
                        <div class="col-md-6">
                            <p><i class="bi bi-exclamation-triangle-fill text-${riskLevelColor}"></i>
                               总问题数: ${summary.total_issues || 0}</p>
                            <p><i class="bi bi-shield-fill-exclamation"></i>
                               风险等级: <span class="badge bg-${riskLevelColor}">${(riskLevel).toUpperCase()}</span></p>
                        </div>
                    </div>
                </div>
            `;
            resultsDiv.appendChild(summaryCard);

            // 获取文件列表，优先使用 suspicious_files，如果不存在则尝试使用 details.suspicious_files
            const suspiciousFiles = result.suspicious_files || 
                                   (result.details ? result.details.suspicious_files : []);

            // 检查是否为单文件分析
            const isSingleFileAnalysis = suspiciousFiles.length === 1;
            
            console.log('是否为单文件分析:', isSingleFileAnalysis);
            console.log('可疑文件:', suspiciousFiles);
            console.log('Python静态分析:', result.python_static);
            console.log('AI验证结果:', result.ai_verification);

            // 单文件分析时的特殊处理
            if (isSingleFileAnalysis) {
                const file = suspiciousFiles[0];
                const singleFileCard = document.createElement('div');
                singleFileCard.className = 'card mb-4';
                
                // 构建单文件分析的HTML
                let fileHtml = `
                    <div class="card-header">
                        <h5 class="mb-0">代码分析结果</h5>
                    </div>
                    <div class="card-body">
                `;
                
                // 添加问题列表
                // 详细记录问题数据结构
                console.log('单文件分析问题数据详情:', file);
                console.log('单文件分析问题数据类型:', typeof file.issues, file.issues);
                
                // 尝试多种方式提取问题
                let issues = [];
                
                // 方法1: 直接使用 file.issues (如果是数组)
                if (Array.isArray(file.issues)) {
                    console.log('使用方法1: 直接使用file.issues数组');
                    issues = file.issues;
                } 
                // 方法2: 从对象中提取问题数组
                else if (file.issues && typeof file.issues === 'object') {
                    console.log('使用方法2: 从对象中提取问题数组');
                    for (const key in file.issues) {
                        if (Array.isArray(file.issues[key])) {
                            console.log(`从key=${key}中提取问题数组, 长度:`, file.issues[key].length);
                            issues.push(...file.issues[key]);
                        }
                    }
                }
                // 方法3: 尝试从file.security_issues获取
                if (issues.length === 0 && Array.isArray(file.security_issues)) {
                    console.log('使用方法3: 从file.security_issues获取');
                    issues = file.security_issues;
                }
                // 方法4: 尝试从file.static_issues获取
                if (issues.length === 0 && Array.isArray(file.static_issues)) {
                    console.log('使用方法4: 从file.static_issues获取');
                    issues = file.static_issues;
                }
                
                console.log('处理后的单文件问题数组:', issues);
                
                if (issues && issues.length > 0) {
                    fileHtml += `
                        <div class="mb-4">
                            <h6><i class="bi bi-bug"></i> 发现的问题 (${issues.length}):</h6>
                            <div class="list-group">
                    `;
                    
                    issues.forEach(issue => {
                        console.log('处理问题:', issue);
                        const severityClass = issue.severity === 'high' ? 'danger' :
                                                   issue.severity === 'medium' ? 'warning' : 'info';
                                                   
                        fileHtml += `
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="badge bg-${severityClass} me-2">
                                        ${issue.severity ? issue.severity.toUpperCase() : 'INFO'}
                                    </span>
                                    <span class="flex-grow-1 ms-2">${issue.description || issue.message || '未知问题'}</span>
                                    ${issue.tool ? `<span class="badge bg-secondary">${issue.tool}</span>` : ''}
                                </div>
                                ${issue.line ? `
                                    <small class="text-muted">
                                        <i class="bi bi-code-slash"></i> 行号: ${issue.line}
                                    </small>
                                ` : ''}
                            </div>
                        `;
                    });
                    
                    fileHtml += `
                        </div>
                    </div>
                `;
                } else {
                    fileHtml += `
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle"></i> 未发现问题
                        </div>
                    `;
                }
                
                // 添加Python静态分析结果
                // 优先使用文件中的python_static，如果不存在则使用全局的python_static
                const pythonStatic = file.python_static || result.python_static;
                console.log('Python静态分析结果:', pythonStatic);
                
                if (pythonStatic) {
                    fileHtml += renderPythonStaticAnalysis(pythonStatic);
                }
                
                // 添加AI分析结果
                if (result.ai_verification) {
                    // 对于单文件分析，ai_verification 可能是以文件路径为键的对象
                    // 或者直接是分析结果对象
                    let aiData = result.ai_verification;
                    console.log('AI验证原始数据:', aiData);
                    
                    // 如果 ai_verification 是以文件路径为键的对象，获取第一个键的值
                    if (typeof aiData === 'object' && !aiData.ai_analysis && Object.keys(aiData).length > 0) {
                        const firstKey = Object.keys(aiData)[0];
                        aiData = aiData[firstKey];
                        console.log('从键中提取的AI数据:', aiData);
                    }
                    
                    fileHtml += renderAIAnalysis(aiData);
                }
                
                fileHtml += `</div>`; // 关闭card-body
                singleFileCard.innerHTML = fileHtml;
                resultsDiv.appendChild(singleFileCard);
            } else {
                // 多文件分析的处理
                const filesCard = document.createElement('div');
                filesCard.className = 'card mb-4';
                filesCard.innerHTML = `
                    <div class="card-header">
                        <h5 class="mb-0">文件分析结果</h5>
                    </div>
                    <div class="card-body">
                        <div class="accordion" id="filesAccordion">
                            ${suspiciousFiles.map((file, index) => {
                                // 详细记录问题数据结构
                                console.log(`文件${index}分析问题数据详情:`, file);
                                console.log(`文件${index}问题数据类型:`, typeof file.issues, file.issues);
                                
                                // 尝试多种方式提取问题
                                let issues = [];
                                
                                // 方法1: 直接使用 file.issues (如果是数组)
                                if (Array.isArray(file.issues)) {
                                    console.log(`文件${index}使用方法1: 直接使用file.issues数组`);
                                    issues = file.issues;
                                } 
                                // 方法2: 从对象中提取问题数组
                                else if (file.issues && typeof file.issues === 'object') {
                                    console.log(`文件${index}使用方法2: 从对象中提取问题数组`);
                                    for (const key in file.issues) {
                                        if (Array.isArray(file.issues[key])) {
                                            console.log(`文件${index}从key=${key}中提取问题数组, 长度:`, file.issues[key].length);
                                            issues.push(...file.issues[key]);
                                        }
                                    }
                                }
                                // 方法3: 尝试从file.security_issues获取
                                if (issues.length === 0 && Array.isArray(file.security_issues)) {
                                    console.log(`文件${index}使用方法3: 从file.security_issues获取`);
                                    issues = file.security_issues;
                                }
                                // 方法4: 尝试从file.static_issues获取
                                if (issues.length === 0 && Array.isArray(file.static_issues)) {
                                    console.log(`文件${index}使用方法4: 从file.static_issues获取`);
                                    issues = file.static_issues;
                                }
                                
                                console.log(`文件${index}处理后的问题数组:`, issues);
                                
                                // 计算文件的风险等级
                                let fileRiskLevel = 'info';
                                if (issues.some(issue => issue.severity === 'high')) {
                                    fileRiskLevel = 'high';
                                } else if (issues.some(issue => issue.severity === 'medium')) {
                                    fileRiskLevel = 'medium';
                                }
                                
                                const fileRiskColor = fileRiskLevel === 'high' ? 'danger' : 
                                                     fileRiskLevel === 'medium' ? 'warning' : 'success';
                                
                                return `
                                    <div class="accordion-item">
                                        <h2 class="accordion-header" id="heading${index}">
                                            <button class="accordion-button ${index === 0 ? '' : 'collapsed'}" 
                                                    type="button" data-bs-toggle="collapse" 
                                                    data-bs-target="#collapse${index}" 
                                                    aria-expanded="${index === 0 ? 'true' : 'false'}" 
                                                    aria-controls="collapse${index}">
                                                <div class="d-flex justify-content-between align-items-center w-100">
                                                    <span>
                                                        <i class="bi bi-file-earmark-code me-2"></i>
                                                        ${file.file_path.split('/').pop() || file.file_path.split('\\').pop() || file.file_path}
                                                    </span>
                                                    <span class="badge bg-${fileRiskColor} ms-2">
                                                        ${issues.length} 个问题
                                                    </span>
                                                </div>
                                            </button>
                                        </h2>
                                        <div id="collapse${index}" 
                                             class="accordion-collapse collapse ${index === 0 ? 'show' : ''}" 
                                             aria-labelledby="heading${index}" 
                                             data-bs-parent="#filesAccordion">
                                            <div class="accordion-body">
                                                ${issues && issues.length > 0 ? `
                                                    <div class="mb-3">
                                                        <h6>发现的问题:</h6>
                                                        <div class="list-group">
                                                            ${issues.map(issue => {
                                                                const severityClass = issue.severity === 'high' ? 'danger' :
                                                                                     issue.severity === 'medium' ? 'warning' : 'info';
                                                                return `
                                                                    <div class="list-group-item">
                                                                        <div class="d-flex justify-content-between align-items-center">
                                                                            <span class="badge bg-${severityClass} me-2">
                                                                                ${issue.severity.toUpperCase()}
                                                                            </span>
                                                                            <span class="flex-grow-1 ms-2">${issue.description}</span>
                                                                            ${issue.tool ? `<span class="badge bg-secondary">${issue.tool}</span>` : ''}
                                                                        </div>
                                                                        ${issue.line ? `
                                                                            <small class="text-muted">
                                                                                <i class="bi bi-code-slash"></i> 行号: ${issue.line}
                                                                            </small>
                                                                        ` : ''}
                                                                    </div>
                                                                `;
                                                            }).join('')}
                                                        </div>
                                                    </div>
                                                ` : `
                                                    <div class="alert alert-success">
                                                        <i class="bi bi-check-circle"></i> 未发现问题
                                                    </div>
                                                `}
                                                
                                                ${file.python_static ? renderPythonStaticAnalysis(file.python_static) : ''}
                                                
                                                ${result.ai_verification && result.ai_verification[file.file_path] ? 
                                                  renderAIAnalysis(result.ai_verification[file.file_path]) : ''}
                                            </div>
                                        </div>
                                    </div>
                                `;
                            }).join('')}
                        </div>
                    </div>
                `;
                resultsDiv.appendChild(filesCard);
            }
        }

        // 渲染AI分析结果
        function renderAIAnalysis(aiData) {
            console.log('渲染AI分析数据:', aiData);
            
            if (!aiData) {
                console.log('无AI分析数据');
                return `
                    <div class="alert alert-info mt-4">
                        <i class="bi bi-info-circle"></i> 未获取到AI分析结果
                    </div>
                `;
            }
            
            // 尝试获取AI分析结果
            let aiAnalysis = null;
            
            // 方法1: 从aiData.ai_analysis获取
            if (aiData.ai_analysis) {
                console.log('从aiData.ai_analysis获取分析结果');
                aiAnalysis = aiData.ai_analysis;
            } 
            // 方法2: 直接使用aiData
            else if (typeof aiData === 'object') {
                console.log('直接使用aiData作为分析结果');
                aiAnalysis = aiData;
            }
            
            if (!aiAnalysis) {
                console.log('无法获取AI分析结果');
                return `
                    <div class="alert alert-info mt-4">
                        <i class="bi bi-info-circle"></i> 未获取到有效的AI分析结果
                    </div>
                `;
            }
            
            console.log('处理后的AI分析结果:', aiAnalysis);
            
            // 构建HTML
            let html = `
                <div class="mt-4">
                    <h5><i class="bi bi-robot"></i> AI代码分析</h5>
            `;
            
            // 尝试解析分析内容
            try {
                // 获取raw_text
                let rawText = '';
                if (aiAnalysis.analysis && aiAnalysis.analysis.raw_text) {
                    rawText = aiAnalysis.analysis.raw_text;
                } else if (aiAnalysis.raw_text) {
                    rawText = aiAnalysis.raw_text;
                }
                
                // 如果有raw_text，尝试解析为JSON对象
                if (rawText) {
                    try {
                        const analysisData = JSON.parse(rawText);
                        console.log('成功解析raw_text为JSON对象:', analysisData);
                        
                        // 代码质量部分
                        if (analysisData.code_quality) {
                            html += `
                                <div class="card mb-3">
                                    <div class="card-header bg-primary text-white">
                                        <h6 class="mb-0">代码质量评估</h6>
                                    </div>
                                    <div class="card-body">
                                        <ul class="list-group list-group-flush">
                                            <li class="list-group-item">
                                                <strong>代码结构和组织:</strong> ${analysisData.code_quality.structure || '未提供'}
                                            </li>
                                            <li class="list-group-item">
                                                <strong>命名规范和一致性:</strong> ${analysisData.code_quality.naming || '未提供'}
                                            </li>
                                            <li class="list-group-item">
                                                <strong>代码复杂度和可维护性:</strong> ${analysisData.code_quality.complexity || '未提供'}
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            `;
                        }
                        
                        // 功能分析部分
                        if (analysisData.functionality) {
                            html += `
                                <div class="card mb-3">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0">功能分析</h6>
                                    </div>
                                    <div class="card-body">
                                        <ul class="list-group list-group-flush">
                                            <li class="list-group-item">
                                                <strong>代码的主要功能和目的:</strong> ${analysisData.functionality.purpose || '未提供'}
                                            </li>
                                            <li class="list-group-item">
                                                <strong>关键算法和逻辑流程:</strong> ${analysisData.functionality.logic_flow || '未提供'}
                                            </li>
                                            <li class="list-group-item">
                                                <strong>潜在的边界情况和异常处理:</strong> ${analysisData.functionality.edge_cases || '未提供'}
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            `;
                        }
                        
                        // 问题确认部分
                        if (analysisData.issue_confirmation && Object.keys(analysisData.issue_confirmation).length > 0) {
                            html += `
                                <div class="card mb-3">
                                    <div class="card-header bg-warning">
                                        <h6 class="mb-0">问题确认与改进建议</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="list-group">
                            `;
                            
                            for (const [issueType, issueData] of Object.entries(analysisData.issue_confirmation)) {
                                const isValidText = issueData.is_valid ? '问题存在' : '问题不存在';
                                const badgeClass = issueData.is_valid ? 'bg-danger' : 'bg-success';
                                
                                html += `
                                    <div class="list-group-item">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span class="badge ${badgeClass} me-2">${isValidText}</span>
                                            <span class="flex-grow-1 ms-2">${issueType}</span>
                                        </div>
                                        <p class="mt-2 mb-1"><strong>问题证据:</strong> ${issueData.evidence || '未提供'}</p>
                                        <p class="mb-0"><strong>改进建议:</strong> ${issueData.improvement || '未提供'}</p>
                                    </div>
                                `;
                            }
                            
                            html += `
                                    </div>
                                </div>
                            `;
                        }
                        
                        // 优化部分
                        if (analysisData.optimization) {
                            html += `
                                <div class="card mb-3">
                                    <div class="card-header bg-success text-white">
                                        <h6 class="mb-0">性能与优化</h6>
                                    </div>
                                    <div class="card-body">
                                        <ul class="list-group list-group-flush">
                                            <li class="list-group-item">
                                                <strong>性能瓶颈分析:</strong> ${analysisData.optimization.performance_bottlenecks || '未提供'}
                                            </li>
                                            <li class="list-group-item">
                                                <strong>优化建议:</strong> ${analysisData.optimization.optimization_suggestions || '未提供'}
                                            </li>
                                            <li class="list-group-item">
                                                <strong>资源使用效率分析:</strong> ${analysisData.optimization.resource_efficiency || '未提供'}
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            `;
                        }
                        
                        // 最佳实践部分
                        if (analysisData.best_practices) {
                            html += `
                                <div class="card mb-3">
                                    <div class="card-header bg-secondary text-white">
                                        <h6 class="mb-0">最佳实践</h6>
                                    </div>
                                    <div class="card-body">
                                        <ul class="list-group list-group-flush">
                                            <li class="list-group-item">
                                                <strong>代码级别的改进建议:</strong> ${analysisData.best_practices.code_level_improvements || '未提供'}
                                            </li>
                                            <li class="list-group-item">
                                                <strong>编码标准建议:</strong> ${analysisData.best_practices.coding_standards || '未提供'}
                                            </li>
                                            <li class="list-group-item">
                                                <strong>适用的设计模式建议:</strong> ${analysisData.best_practices.design_patterns || '未提供'}
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            `;
                        }
                        
                        // 如果解析成功，直接返回
                        html += '</div>'; // 关闭最外层div
                        return html;
                        
                    } catch (e) {
                        console.error('解析raw_text为JSON失败:', e);
                        // 解析失败，显示原始文本
                        html += `
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle"></i> 无法解析为标准格式，显示原始分析结果
                            </div>
                            <div class="card mb-3">
                                <div class="card-header bg-secondary text-white">
                                    <h6 class="mb-0">分析结果 (JSON格式)</h6>
                                </div>
                                <div class="card-body">
                                    <pre class="bg-light p-3 rounded">${rawText}</pre>
                                </div>
                            </div>
                        `;
                    }
                } else {
                    // 没有raw_text，尝试使用recommendations
                    if (aiAnalysis.analysis && aiAnalysis.analysis.recommendations && aiAnalysis.analysis.recommendations.length > 0) {
                        html += `
                            <div class="card mb-3">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">分析建议</h6>
                                </div>
                                <div class="card-body">
                                    <div class="list-group">
                        `;
                        
                        aiAnalysis.analysis.recommendations.forEach(rec => {
                            html += `
                                <div class="list-group-item">
                                    <strong>${rec.issue}:</strong> ${rec.solution}
                                </div>
                            `;
                        });
                        
                        html += `
                                </div>
                            </div>
                        `;
                    }
                    
                    // 尝试使用issues
                    if (aiAnalysis.analysis && aiAnalysis.analysis.issues && aiAnalysis.analysis.issues.length > 0) {
                        html += `
                            <div class="card mb-3">
                                <div class="card-header bg-warning">
                                    <h6 class="mb-0">发现的问题</h6>
                                </div>
                                <div class="card-body">
                                    <div class="list-group">
                        `;
                        
                        aiAnalysis.analysis.issues.forEach(issue => {
                            const isValidText = issue.is_valid ? '问题存在' : '问题不存在';
                            const badgeClass = issue.is_valid ? 'bg-danger' : 'bg-success';
                            
                            html += `
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="badge ${badgeClass} me-2">${isValidText}</span>
                                        <span class="flex-grow-1 ms-2">${issue.type || '未知问题'}</span>
                                    </div>
                                    <p class="mt-2 mb-0">${issue.description || '未提供描述'}</p>
                                </div>
                            `;
                        });
                        
                        html += `
                                </div>
                            </div>
                        `;
                    }
                    
                    // 如果没有结构化数据，显示JSON
                    if (!aiAnalysis.analysis || (!aiAnalysis.analysis.recommendations && !aiAnalysis.analysis.issues)) {
                        html += `
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle"></i> 未找到结构化分析数据，显示原始数据
                            </div>
                            <div class="card mb-3">
                                <div class="card-header bg-secondary text-white">
                                    <h6 class="mb-0">原始分析数据</h6>
                                </div>
                                <div class="card-body">
                                    <pre class="bg-light p-3 rounded">${JSON.stringify(aiAnalysis, null, 2)}</pre>
                                </div>
                            </div>
                        `;
                    }
                }
                
            } catch (e) {
                console.error('渲染AI分析结果时出错:', e);
                html += `
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle"></i> 渲染分析结果时出错: ${e.message}
                    </div>
                    <pre class="bg-light p-3 rounded">${JSON.stringify(aiAnalysis, null, 2)}</pre>
                `;
            }
            
            html += '</div>'; // 关闭最外层div
            return html;
        }

        // 渲染Python静态分析结果
        function renderPythonStaticAnalysis(pythonStatic) {
            if (!pythonStatic) {
                console.log('无Python静态分析结果');
                return '';
            }
            
            console.log('渲染Python静态分析结果:', pythonStatic);
            
            let html = `
                <div class="mt-4">
                    <h6><i class="bi bi-file-earmark-code"></i> Python 静态分析结果:</h6>
            `;
            
            try {
                // 处理Pyflakes结果
                html += `
                    <div class="mb-3">
                        <h6>基础逻辑检查 (Pyflakes):</h6>
                `;
                
                const pyflakes = pythonStatic.pyflakes || {};
                console.log('Pyflakes结果:', pyflakes);
                
                if (!pyflakes.issues || pyflakes.issues.length === 0) {
                    html += '<div class="alert alert-success">未发现基础逻辑错误</div>';
                } else {
                    html += '<div class="list-group">';
                    pyflakes.issues.forEach(issue => {
                        console.log('Pyflakes问题:', issue);
                        html += `
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="badge bg-${issue.severity === 'high' ? 'danger' :
                                          issue.severity === 'medium' ? 'warning' : 'info'} me-2">
                                        ${issue.severity ? issue.severity.toUpperCase() : 'INFO'}
                                    </span>
                                    <span class="flex-grow-1 ms-2">${issue.type ? `${issue.type}: ` : ''}${issue.description || '未知问题'}</span>
                                </div>
                                <small class="text-muted">
                                    <i class="bi bi-code-slash"></i> 行 ${issue.line || '?'}
                                </small>
                            </div>
                        `;
                    });
                    html += '</div>';
                }
                
                html += '</div>';
                
                // 处理Ruff结果
                html += `
                    <div class="mb-3">
                        <h6>中高级逻辑检查 (Ruff):</h6>
                `;
                
                const ruff = pythonStatic.ruff || {};
                console.log('Ruff结果:', ruff);
                
                if (!ruff.issues || ruff.issues.length === 0) {
                    html += '<div class="alert alert-success">未发现中高级逻辑错误</div>';
                } else {
                    html += '<div class="list-group">';
                    ruff.issues.forEach(issue => {
                        console.log('Ruff问题:', issue);
                        html += `
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="badge bg-${issue.severity === 'high' ? 'danger' :
                                          issue.severity === 'medium' ? 'warning' : 'info'} me-2">
                                        ${issue.severity ? issue.severity.toUpperCase() : 'INFO'}
                                    </span>
                                    <span class="flex-grow-1 ms-2">${issue.type ? `${issue.type}: ` : ''}${issue.description || '未知问题'}</span>
                                    ${issue.fixable ? '<span class="badge bg-success">可修复</span>' : ''}
                                </div>
                                <small class="text-muted">
                                    <i class="bi bi-code-slash"></i> 行 ${issue.line || '?'}${issue.column ? `:${issue.column}` : ''}
                                    ${issue.code ? `<br><code>${issue.code}</code>` : ''}
                                </small>
                            </div>
                        `;
                    });
                    html += '</div>';
                    
                    // 显示修复建议
                    if (ruff.fixes && ruff.fixes.length > 0) {
                        html += `
                            <div class="mt-3">
                                <h6>自动修复建议:</h6>
                                <div class="list-group">
                                    ${ruff.fixes.map(fix => `
                                        <div class="list-group-item">
                                            <p>${fix.description || '未知修复'}</p>
                                            ${fix.diff ? `<pre class="bg-light p-2"><code>${fix.diff}</code></pre>` : ''}
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        `;
                    }
                }
                
                html += '</div>';
                
                // 处理Pylint结果
                html += `
                    <div class="mb-3">
                        <h6>深度逻辑检查 (Pylint):</h6>
                `;
                
                const pylint = pythonStatic.pylint || {};
                console.log('Pylint结果:', pylint);
                
                if (!pylint.issues || pylint.issues.length === 0) {
                    html += '<div class="alert alert-success">未发现深度分析问题</div>';
                } else {
                    html += '<div class="list-group">';
                    pylint.issues.forEach(issue => {
                        console.log('Pylint问题:', issue);
                        html += `
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="badge bg-${issue.severity === 'high' ? 'danger' :
                                          issue.severity === 'medium' ? 'warning' : 'info'} me-2">
                                        ${issue.severity ? issue.severity.toUpperCase() : 'INFO'}
                                    </span>
                                    <span class="flex-grow-1 ms-2">${issue.description || '未知问题'}</span>
                                </div>
                                ${issue.line ? `
                                    <small class="text-muted">
                                        <i class="bi bi-code-slash"></i> 行号: ${issue.line}
                                    </small>
                                ` : ''}
                            </div>
                        `;
                    });
                    html += '</div>';
                }
                
                html += '</div>';
                
                // 添加摘要信息
                const summary = pythonStatic.summary || {};
                if (summary) {
                    console.log('Python静态分析摘要:', summary);
                    
                    html += `
                        <div class="mb-3">
                            <h6>分析摘要:</h6>
                            <div class="card">
                                <div class="card-body">
                                    <p><strong>总问题数:</strong> ${summary.total_issues || 0}</p>
                                    <p><strong>风险等级:</strong> 
                                        <span class="badge bg-${
                                            summary.risk_level === 'high' ? 'danger' :
                                            summary.risk_level === 'medium' ? 'warning' : 'info'
                                        }">
                                            ${summary.risk_level ? summary.risk_level.toUpperCase() : 'INFO'}
                                        </span>
                                    </p>
                                    ${summary.pylint_score ? `<p><strong>Pylint评分:</strong> ${summary.pylint_score}</p>` : ''}
                                </div>
                            </div>
                        </div>
                    `;
                    
                    // 显示建议
                    if (summary.recommendations && summary.recommendations.length > 0) {
                        html += `
                            <div class="mb-3">
                                <h6>改进建议:</h6>
                                <ul class="list-group">
                                    ${summary.recommendations.map(rec => `
                                        <li class="list-group-item">${rec}</li>
                                    `).join('')}
                                </ul>
                            </div>
                        `;
                    }
                }
                
            } catch (e) {
                console.error('渲染Python静态分析结果失败:', e);
                html += `
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle"></i> 渲染Python静态分析结果时发生错误: ${e.message}
                    </div>
                `;
            }
            
            html += '</div>';
            return html;
        }

        // 渲染Pyflakes标签页
        function renderPyflakesTab(pyflakes) {
            if (!pyflakes) {
                return '<div class="alert alert-info">无Pyflakes分析数据</div>';
            }
            
            let html = `
                <div class="tool-header mb-3">
                    <h6>${pyflakes.tool_info?.name || 'Pyflakes'} - ${pyflakes.tool_info?.description || '基础逻辑错误检测'}</h6>
                    <p>发现 ${pyflakes.total_issues || 0} 个基础逻辑错误</p>
                </div>
            `;
            
            if (!pyflakes.issues || pyflakes.issues.length === 0) {
                html += '<div class="alert alert-success">未发现基础逻辑错误</div>';
            } else {
                html += '<div class="list-group">';
                pyflakes.issues.forEach(issue => {
                    html += `
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="badge bg-${issue.severity === 'high' ? 'danger' :
                                      issue.severity === 'medium' ? 'warning' : 'info'} me-2">
                                    ${issue.severity.toUpperCase()}
                                </span>
                                <span class="flex-grow-1 ms-2">${issue.type ? `${issue.type}: ` : ''}${issue.description}</span>
                            </div>
                            <small class="text-muted">
                                <i class="bi bi-code-slash"></i> 行 ${issue.line}
                            </small>
                        </div>
                    `;
                });
                html += '</div>';
            }
            
            return html;
        }

        // 渲染Ruff标签页
        function renderRuffTab(ruff) {
            if (!ruff) {
                return '<div class="alert alert-info">无Ruff分析数据</div>';
            }
            
            let html = `
                <div class="tool-header mb-3">
                    <h6>${ruff.tool_info?.name || 'Ruff'} - ${ruff.tool_info?.description || '中高级逻辑错误检测'}</h6>
                    <p>发现 ${ruff.total_issues || 0} 个中高级逻辑错误</p>
                    ${ruff.fixes?.length > 0 ? `<p class="text-success">可自动修复 ${ruff.fixes.length} 个问题</p>` : ''}
                </div>
            `;
            
            if (!ruff.issues || ruff.issues.length === 0) {
                html += '<div class="alert alert-success">未发现中高级逻辑错误</div>';
            } else {
                html += '<div class="list-group">';
                ruff.issues.forEach(issue => {
                    html += `
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="badge bg-${issue.severity === 'high' ? 'danger' :
                                      issue.severity === 'medium' ? 'warning' : 'info'} me-2">
                                    ${issue.severity.toUpperCase()}
                                </span>
                                <span class="flex-grow-1 ms-2">${issue.type ? `${issue.type}: ` : ''}${issue.description}</span>
                                ${issue.fixable ? '<span class="badge bg-success">可修复</span>' : ''}
                            </div>
                            <small class="text-muted">
                                <i class="bi bi-code-slash"></i> 行 ${issue.line}${issue.column ? `:${issue.column}` : ''}
                                ${issue.code ? `<br><code>${issue.code}</code>` : ''}
                            </small>
                        </div>
                    `;
                });
                html += '</div>';
                
                // 显示修复建议
                if (ruff.fixes && ruff.fixes.length > 0) {
                    html += `
                        <div class="mt-3">
                            <h6>自动修复建议:</h6>
                            <div class="list-group">
                                ${ruff.fixes.map(fix => `
                                    <div class="list-group-item">
                                        <p>${fix.description}</p>
                                        ${fix.diff ? `<pre class="bg-light p-2"><code>${fix.diff}</code></pre>` : ''}
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    `;
                }
            }
            
            return html;
        }

        // 渲染Pylint标签页
        function renderPylintTab(pylint) {
            if (!pylint) {
                return '<div class="alert alert-info">无Pylint分析数据</div>';
            }
            
            let html = `
                <div class="tool-header mb-3">
                    <h6>${pylint.tool_info?.name || 'Pylint'} - ${pylint.tool_info?.description || '深度代码分析'}</h6>
                    <p>发现 ${pylint.total_issues || 0} 个深度分析问题</p>
                    ${pylint.score !== undefined ? `<p>代码质量评分: <strong>${pylint.score.toFixed(2)}/10</strong></p>` : ''}
                </div>
            `;
            
            // 显示统计信息
            if (pylint.stats && Object.keys(pylint.stats).length > 0) {
                html += `
                    <div class="card mb-3">
                        <div class="card-header">统计信息</div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <h6>按严重程度</h6>
                                    <ul class="list-group">
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            高严重性
                                            <span class="badge bg-danger rounded-pill">${pylint.stats.by_severity?.high || 0}</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            中等严重性
                                            <span class="badge bg-warning rounded-pill">${pylint.stats.by_severity?.medium || 0}</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            低严重性
                                            <span class="badge bg-info rounded-pill">${pylint.stats.by_severity?.low || 0}</span>
                                        </li>
                                    </ul>
                                </div>
                                <div class="col-md-8">
                                    <h6>按类型</h6>
                                    <ul class="list-group">
                                        ${Object.entries(pylint.stats.by_type || {}).map(([type, count]) => `
                                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                                ${type}
                                                <span class="badge bg-primary rounded-pill">${count}</span>
                                            </li>
                                        `).join('')}
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            if (!pylint.issues || pylint.issues.length === 0) {
                html += '<div class="alert alert-success">未发现深度分析问题</div>';
            } else {
                html += '<div class="list-group">';
                pylint.issues.forEach(issue => {
                    html += `
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="badge bg-${issue.severity === 'high' ? 'danger' :
                                      issue.severity === 'medium' ? 'warning' : 'info'} me-2">
                                    ${issue.severity.toUpperCase()}
                                </span>
                                <span class="flex-grow-1 ms-2">${issue.description}</span>
                            </div>
                            ${issue.line ? `
                                <small class="text-muted">
                                    <i class="bi bi-code-slash"></i> 行号: ${issue.line}
                                </small>
                            ` : ''}
                        </div>
                    `;
                });
                html += '</div>';
            }
            
            return html;
        }

        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('results').style.display = 'none';
        }

        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }

        // 添加主题切换功能
        function toggleTheme() {
            const body = document.body;
            const themeIcon = document.getElementById('themeIcon');
            const currentTheme = body.getAttribute('data-theme');

            if (currentTheme === 'dark') {
                body.removeAttribute('data-theme');
                themeIcon.className = 'bi bi-moon-fill';
                localStorage.setItem('theme', 'light');
            } else {
                body.setAttribute('data-theme', 'dark');
                themeIcon.className = 'bi bi-sun-fill';
                localStorage.setItem('theme', 'dark');
            }
        }

        // 初始化主题
        function initTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            const themeIcon = document.getElementById('themeIcon');

            if (savedTheme === 'dark') {
                document.body.setAttribute('data-theme', 'dark');
                themeIcon.className = 'bi bi-sun-fill';
            } else {
                document.body.removeAttribute('data-theme');
                themeIcon.className = 'bi bi-moon-fill';
            }
        }

        // 添加项目类型切换监听
        document.getElementById('projectType').addEventListener('change', function() {
            const uploadType = document.getElementById('uploadType').value;
            if (uploadType === 'folder') {
                const folderInput = document.getElementById('codeFolder');
                if (folderInput.files && folderInput.files.length > 0) {
                    // 重新扫描已选择的文件
                    updateFileList(folderInput.files, 'folderFileList');
                }
            }
        });

        // 功能切换
        function switchToFeature(feature) {
            // 隐藏所有功能区域
            document.getElementById('codeAnalysisSection').style.display = 'none';
            document.getElementById('results').style.display = 'none';
            document.getElementById('tutoringSection').style.display = 'none';
            document.getElementById('syntaxCheckSection').style.display = 'none';

            // 显示选择的功能区域
            if (feature === 'codeAnalysis') {
                document.getElementById('codeAnalysisSection').style.display = 'block';
                if (document.getElementById('auditResults').innerHTML.trim()) {
                    document.getElementById('results').style.display = 'block';
                }
            } else if (feature === 'tutoring') {
                document.getElementById('tutoringSection').style.display = 'block';

                // 如果是第一次打开辅导页面，添加欢迎消息
                if (document.getElementById('messages-container').children.length === 0) {
                    addMessage('assistant', '你好！我是你的编程辅导助手。有任何编程问题都可以问我，我会尽力帮助你解决问题。');
                }
            } else if (feature === 'syntaxCheck') {
                document.getElementById('syntaxCheckSection').style.display = 'block';
            }
        }

        // 语法检测文件上传处理
        document.addEventListener('DOMContentLoaded', function() {
            const syntaxFileInput = document.getElementById('syntaxFile');
            const syntaxFileBtn = document.getElementById('syntaxFileBtn');

            // 监听文件上传
            if (syntaxFileInput) {
                syntaxFileInput.addEventListener('change', function() {
                    if (this.files.length > 0) {
                        syntaxFileBtn.disabled = false;
                    } else {
                        syntaxFileBtn.disabled = true;
                    }
                });
            }
        });

        // 检查文件语法
        async function checkSyntaxFile() {
            const fileInput = document.getElementById('syntaxFile');
            if (!fileInput.files.length) return;

            const file = fileInput.files[0];
            const formData = new FormData();
            formData.append('file', file);

            // 显示加载状态
            document.getElementById('loading').style.display = 'block';
            document.getElementById('loadingText').textContent = '语法检测中，请稍候...';
            document.getElementById('syntaxResults').style.display = 'none';

            try {
                const response = await fetch(`${apiUrl}/api/syntax/check/file`, {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error('语法检测请求失败');
                }

                const result = await response.json();
                displaySyntaxResults(result);
            } catch (error) {
                console.error('语法检测失败:', error);
                alert('语法检测过程中发生错误: ' + error.message);
            } finally {
                document.getElementById('loading').style.display = 'none';
            }
        }

        // 检查代码语法
        async function checkSyntaxCode() {
            const codeInput = document.getElementById('codeInput').value.trim();
            const language = document.getElementById('syntaxLanguage').value;

            if (!codeInput) {
                alert('请输入代码');
                return;
            }

            // 显示加载状态
            document.getElementById('loading').style.display = 'block';
            document.getElementById('loadingText').textContent = '语法检测中，请稍候...';
            document.getElementById('syntaxResults').style.display = 'none';

            try {
                const response = await fetch(`${apiUrl}/api/syntax/check`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        code: codeInput,
                        language: language
                    })
                });

                if (!response.ok) {
                    throw new Error('语法检测请求失败');
                }

                const result = await response.json();
                displaySyntaxResults(result);
            } catch (error) {
                console.error('语法检测失败:', error);
                alert('语法检测过程中发生错误: ' + error.message);
            } finally {
                document.getElementById('loading').style.display = 'none';
            }
        }

        // 显示语法检测结果
        function displaySyntaxResults(result) {
            const resultsDiv = document.getElementById('syntaxResults');
            const statusDiv = document.getElementById('syntaxStatus');
            const errorListDiv = document.getElementById('syntaxErrorList');
            const errorItemsDiv = document.getElementById('errorItems');

            // 清空之前的结果
            errorItemsDiv.innerHTML = '';

            // 设置状态信息
            if (result.has_errors) {
                statusDiv.className = 'alert alert-danger';
                statusDiv.innerHTML = `<i class="bi bi-x-circle"></i> ${result.message}`;
            } else {
                statusDiv.className = 'alert alert-success';
                statusDiv.innerHTML = `<i class="bi bi-check-circle"></i> ${result.message}`;
            }

            // 显示错误列表
            if (result.has_errors && result.errors && result.errors.length > 0) {
                errorListDiv.style.display = 'block';

                result.errors.forEach(error => {
                    const errorItem = document.createElement('div');
                    errorItem.className = 'list-group-item';

                    const severityBadge = document.createElement('span');
                    severityBadge.className = `badge ${error.severity === 'error' ? 'bg-danger' : 'bg-warning'} me-2`;
                    severityBadge.textContent = error.severity;

                    const locationSpan = document.createElement('span');
                    locationSpan.className = 'fw-bold me-2';
                    locationSpan.textContent = `行 ${error.line}, 列 ${error.column}:`;

                    const messageSpan = document.createElement('span');
                    messageSpan.textContent = error.message;

                    errorItem.appendChild(severityBadge);
                    errorItem.appendChild(locationSpan);
                    errorItem.appendChild(messageSpan);

                    errorItemsDiv.appendChild(errorItem);
                });
            } else {
                errorListDiv.style.display = 'none';
            }

            // 显示结果区域
            resultsDiv.style.display = 'block';
        }

        // 添加消息到聊天界面
        function addMessage(type, content) {
            const messagesContainer = document.getElementById('messages-container');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}-message`;

            const timestamp = new Date().toLocaleTimeString();

            messageDiv.innerHTML = `
                <div class="message-avatar">
                    <i class="bi bi-${type === 'user' ? 'person-fill' : 'robot'}"></i>
                </div>
                <div class="message-content">
                    <div>${formatMessage(content)}</div>
                    <div class="message-timestamp">${timestamp}</div>
                </div>
            `;

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // 格式化消息内容（支持Markdown）
        function formatMessage(content) {
            // 简单的代码块处理
            content = content.replace(/```(\w*)([\s\S]*?)```/g, '<pre><code class="language-$1">$2</code></pre>');

            // 简单的行内代码处理
            content = content.replace(/`([^`]+)`/g, '<code>$1</code>');

            // 处理换行
            content = content.replace(/\n/g, '<br>');

            return content;
        }

        // 发送问题到服务器
        async function sendQuestion() {
            const questionInput = document.getElementById('question-input');
            const question = questionInput.value.trim();

            if (!question) return;

            // 添加用户消息
            addMessage('user', question);
            questionInput.value = '';

            // 添加思考中提示
            const thinkingDiv = document.createElement('div');
            thinkingDiv.className = 'thinking';
            thinkingDiv.innerHTML = '<i class="bi bi-arrow-repeat spinner"></i> <span>思考中...</span>';
            document.getElementById('messages-container').appendChild(thinkingDiv);

            try {
                // 禁用发送按钮
                document.getElementById('send-button').disabled = true;

                // 发送请求
                const response = await fetch(`${apiUrl}/api/tutoring/ask`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        question: question
                    })
                });

                // 移除思考中提示
                document.getElementById('messages-container').removeChild(thinkingDiv);

                if (response.ok) {
                    const data = await response.json();

                    if (data.status === 'success') {
                        // 添加助手回复
                        addMessage('assistant', data.answer);

                        // 更新主题和难度
                        if (data.understanding) {
                            const topicBadge = document.getElementById('topic-badge');
                            const difficultyBadge = document.getElementById('difficulty-badge');
                            const topicText = document.getElementById('topic-text');
                            const difficultyText = document.getElementById('difficulty-text');

                            if (data.understanding.topic) {
                                topicText.textContent = data.understanding.topic;
                                topicBadge.style.display = 'inline-block';
                            }

                            if (data.understanding.difficulty) {
                                difficultyText.textContent = data.understanding.difficulty;
                                difficultyBadge.style.display = 'inline-block';

                                // 根据难度设置不同的颜色
                                difficultyBadge.className = 'badge mb-2 ms-2';
                                if (data.understanding.difficulty === '初级') {
                                    difficultyBadge.classList.add('bg-success');
                                } else if (data.understanding.difficulty === '中级') {
                                    difficultyBadge.classList.add('bg-warning');
                                } else if (data.understanding.difficulty === '高级') {
                                    difficultyBadge.classList.add('bg-danger');
                                } else {
                                    difficultyBadge.classList.add('bg-info');
                                }
                            }
                        }
                    } else {
                        // 添加错误消息
                        addMessage('assistant', `抱歉，我遇到了一些问题：${data.message || '未知错误'}`);
                    }
                } else {
                    // 添加错误消息
                    addMessage('assistant', '抱歉，服务器返回了错误。请稍后再试。');
                }
            } catch (error) {
                console.error('Error:', error);
                // 移除思考中提示
                if (document.getElementById('messages-container').contains(thinkingDiv)) {
                    document.getElementById('messages-container').removeChild(thinkingDiv);
                }
                // 添加错误消息
                addMessage('assistant', '抱歉，发生了网络错误。请稍后再试。');
            } finally {
                // 启用发送按钮
                document.getElementById('send-button').disabled = false;
            }
        }

        // 初始化智能辅导页面
        function initTutoringPage() {
            // 添加智能辅导页面的事件监听器
            document.getElementById('send-button').addEventListener('click', sendQuestion);
            document.getElementById('question-input').addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendQuestion();
                }
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeUploadTypes();
            initializeFileInputs();
            fetchAvailableModels();
            initTheme();
            initTutoringPage();

            // 显示主题切换按钮
            document.querySelector('.theme-toggle').style.display = 'flex';

            // 默认显示语法检测页面
            switchToFeature('syntaxCheck');
        });
    </script>

    <!-- 添加JSZip库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.7.1/jszip.min.js"></script>

    <!-- 添加Marked库用于Markdown解析 -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
</body>
</html>
