# Python 安全漏洞示例

import os
import subprocess
import pickle
import yaml
import sqlite3
from flask import Flask, request, render_template_string

app = Flask(__name__)

# 命令注入漏洞示例
def command_injection_example(user_input):
    # 直接执行用户输入的命令，存在命令注入风险
    result = os.system(f"ping {user_input}")
    return result

# 不安全的反序列化示例
def unsafe_deserialization(serialized_data):
    # 不安全的 pickle 反序列化
    return pickle.loads(serialized_data)

# 不安全的 YAML 加载示例
def unsafe_yaml_load(yaml_data):
    # 不安全的 YAML 加载，可能导致代码执行
    return yaml.load(yaml_data)

# SQL 注入示例
def sql_injection_example(user_id):
    conn = sqlite3.connect('example.db')
    cursor = conn.cursor()
    # 字符串格式化导致的 SQL 注入
    query = f"SELECT * FROM users WHERE id = {user_id}"
    cursor.execute(query)
    return cursor.fetchall()

# 模板注入示例
@app.route('/template')
def template_injection():
    user_template = request.args.get('template', '')
    # 直接渲染用户提供的模板字符串
    return render_template_string(user_template)

# 不安全的文件操作
def unsafe_file_operation(filename):
    # 未验证的文件路径，可能导致目录遍历
    with open(filename, 'r') as f:
        return f.read()

# 不安全的子进程调用
def unsafe_subprocess(command):
    # 直接执行用户输入的命令
    output = subprocess.check_output(command, shell=True)
    return output

# 不安全的 eval 使用
def unsafe_eval(code):
    # 直接执行用户提供的代码
    return eval(code)

if __name__ == "__main__":
    app.run(debug=True)  # 在生产环境中启用调试模式
