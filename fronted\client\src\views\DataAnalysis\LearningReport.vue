<template>
  <div class="learning-report">
    <van-nav-bar
      title="学习报告"
      left-text="返回"
      left-arrow
      @click-left="$router.back()"
    >
      <template #right>
        <van-button type="primary" size="small" icon="replay" @click="refreshData">刷新数据</van-button>
      </template>
    </van-nav-bar>

    <van-overlay :show="loading">
      <div class="loading-container">
        <van-loading type="spinner" color="#1989fa" />
        <div class="loading-text">加载中...</div>
      </div>
    </van-overlay>

    <div v-if="!report || (report.knowledgePoints && report.knowledgePoints.length === 0 && report.totalCourses === 0)" class="empty-state">
      <van-empty description="暂无学习报告数据" />
      <p class="empty-tip">开始学习课程后，这里将显示您的个人学习报告</p>
    </div>

    <div v-else class="report-container">
      <!-- 学习概览 -->
      <div class="section">
        <h4>学习概览</h4>
        <div class="overview-stats">
          <div class="stat-item">
            <div class="stat-value">{{ report.totalCourses || 0 }}</div>
            <div class="stat-label">学习课程</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ report.totalHours || 0 }}</div>
            <div class="stat-label">学习小时</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ report.completionRate || 0 }}%</div>
            <div class="stat-label">完成率</div>
          </div>
        </div>
      </div>

      <!-- 知识掌握情况 -->
      <div class="section">
        <h4>知识掌握情况</h4>
        <div v-if="!report.knowledgePoints || report.knowledgePoints.length === 0" class="empty-state">
          <van-empty description="暂无知识点掌握数据" />
          <p class="empty-tip">开始学习课程后，这里将显示您的知识点掌握情况</p>
        </div>
        <div v-else class="radar-chart-container">
          <canvas ref="radarChart"></canvas>
        </div>
      </div>

      <!-- 学习建议 -->
      <div class="section">
        <h4>学习建议</h4>
        <div v-if="!report.suggestions || report.suggestions.length === 0" class="empty-state">
          <van-empty description="暂无学习建议" />
          <p class="empty-tip">开始学习课程后，这里将显示针对您的学习建议</p>
        </div>
        <div v-else class="suggestions">
          <div v-for="(suggestion, index) in report.suggestions" :key="index" class="suggestion">
            <van-icon name="bulb-o" color="#f5a623" />
            <span>{{ suggestion }}</span>
          </div>
        </div>
      </div>

      <!-- 推荐课程 -->
      <div v-if="report.recommendedCourses && report.recommendedCourses.length > 0" class="section">
        <h4>推荐课程</h4>
        <div class="recommended-courses">
          <div v-for="(course, index) in report.recommendedCourses" :key="index" class="course-item" @click="viewCourse(course.id)">
            <van-image
              :src="course.cover.startsWith('http') ? course.cover : $axios.defaults.baseURL + 'file/get/' + course.cover"
              width="60"
              height="60"
              radius="4"
              error-icon="photo-o"
              loading-icon="photo-o"
              @error="handleImageError($event, course)"
            />
            <div class="course-info">
              <div class="course-name">{{ course.name }}</div>
              <div class="course-desc">{{ course.description }}</div>
            </div>
            <van-icon name="arrow" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Chart from 'chart.js';

export default {
  name: 'LearningReport',
  data() {
    return {
      loading: true, // 添加加载状态
      report: {
        totalCourses: 0,
        totalHours: 0,
        completionRate: 0,
        knowledgePoints: [],
        suggestions: [],
        recommendedCourses: []
      },
      radarChart: null
    }
  },
  methods: {
    fetchReport() {
      // 显示加载状态
      this.loading = true;

      console.log('开始获取学习报告数据...');

      // 确保URL路径正确
      const url = 'data_analysis/report/personal';
      console.log('请求学习报告URL:', this.$axios.defaults.baseURL + url);

      // 返回Promise以便链式调用
      return this.$axios.get(url, {
        // 添加超时设置
        timeout: 10000,
        // 添加错误重试
        retry: 3,
        retryDelay: 1000
      })
        .then(res => {
          console.log('学习报告响应状态码:', res.status);
          console.log('学习报告响应头:', res.headers);
          console.log('学习报告响应完整数据:', JSON.stringify(res.data));

          if (res.data && res.data.code === 0) {
            let backendData = res.data.data;
            console.log('后端返回的学习报告数据类型:', typeof backendData);
            console.log('后端返回的学习报告数据是否为null:', backendData === null);
            console.log('后端返回的学习报告数据是否为undefined:', backendData === undefined);

            // 如果后端返回的是字符串，尝试解析为JSON对象
            if (typeof backendData === 'string') {
              try {
                console.log('尝试将字符串解析为JSON对象');
                backendData = JSON.parse(backendData);
                console.log('解析后的数据:', backendData);
              } catch (e) {
                console.error('解析JSON失败:', e);
                // 如果解析失败，尝试使用eval
                try {
                  console.log('尝试使用eval解析');
                  // 使用eval前确保字符串是有效的JSON格式
                  if (backendData.startsWith('{') && backendData.endsWith('}')) {
                    backendData = eval('(' + backendData + ')');
                    console.log('eval解析后的数据:', backendData);
                  }
                } catch (evalError) {
                  console.error('eval解析失败:', evalError);
                }
              }
            }

            console.log('处理后的学习报告数据详情:', JSON.stringify(backendData));

            // 检查数据是否有效
            if (backendData && typeof backendData === 'object') {
              // 更新报告数据
              this.report = {
                // 使用默认值，确保所有必要的属性都存在
                totalCourses: backendData.totalCourses !== undefined ? backendData.totalCourses : 0,
                totalHours: backendData.totalHours !== undefined ? backendData.totalHours : 0,
                completionRate: backendData.completionRate !== undefined ? backendData.completionRate : 0,
                knowledgePoints: Array.isArray(backendData.knowledgePoints) ? backendData.knowledgePoints : [],
                suggestions: Array.isArray(backendData.suggestions) ? backendData.suggestions : ['开始学习课程后，这里将显示针对您的学习建议'],
                recommendedCourses: Array.isArray(backendData.recommendedCourses) ? backendData.recommendedCourses : []
              };

              // 确保knowledgePoints中的每个元素都有必要的属性
              if (this.report.knowledgePoints.length > 0) {
                this.report.knowledgePoints = this.report.knowledgePoints.map(kp => {
                  return {
                    name: kp.name || '未命名知识点',
                    mastery_level: kp.mastery_level !== undefined ? kp.mastery_level : 0
                  };
                });
              }

              console.log('处理后的报告数据:', this.report);

              // 渲染雷达图
              this.$nextTick(() => {
                this.renderRadarChart();
              });

              this.$toast.success('获取学习报告成功');
            } else {
              console.error('后端返回的数据无效:', backendData);
              this.$toast.fail('获取学习报告失败，数据格式不正确');
              this.initEmptyReport();
            }
          } else {
            console.error('获取学习报告失败:', res.data);
            this.$toast.fail('获取学习报告失败，请稍后再试');
            this.initEmptyReport();
          }
        })
        .catch(err => {
          console.error('获取学习报告出错:', err);
          console.error('错误详情:', err.message);
          console.error('错误堆栈:', err.stack);
          this.$toast.fail('获取学习报告出错，请稍后再试');
          this.initEmptyReport();
        })
        .finally(() => {
          this.loading = false;
        });
    },

    initEmptyReport() {
      // 初始化一个空的报告对象
      this.report = {
        totalCourses: 0,
        totalHours: 0,
        completionRate: 0,
        knowledgePoints: [],
        suggestions: ['开始学习课程后，这里将显示针对您的学习建议'],
        recommendedCourses: []
      };
      this.renderRadarChart();
    },
    renderRadarChart() {
      console.log('开始渲染雷达图');

      if (this.radarChart) {
        console.log('销毁旧的雷达图');
        this.radarChart.destroy();
        this.radarChart = null;
      }

      if (!this.$refs.radarChart) {
        console.error('雷达图canvas元素不存在');
        // 延迟重试一次
        setTimeout(() => {
          if (this.$refs.radarChart) {
            console.log('延迟后找到雷达图canvas元素，重新渲染');
            this.renderRadarChart();
          }
        }, 500);
        return;
      }

      const ctx = this.$refs.radarChart.getContext('2d');

      // 确保有知识点数据
      if (!this.report.knowledgePoints || this.report.knowledgePoints.length === 0) {
        console.warn('没有知识点掌握情况数据');
        // 创建一个空图表
        this.radarChart = new Chart(ctx, {
          type: 'radar',
          data: {
            labels: ['暂无数据'],
            datasets: [{
              label: '掌握程度 (%)',
              data: [0],
              backgroundColor: 'rgba(74, 108, 247, 0.2)',
              borderColor: '#4a6cf7'
            }]
          },
          options: {
            scale: {
              ticks: {
                beginAtZero: true,
                max: 100,
                stepSize: 20
              }
            },
            responsive: true,
            maintainAspectRatio: false
          }
        });
        return;
      }

      // 限制显示的知识点数量，避免雷达图过于拥挤
      const maxPoints = 8;
      let knowledgePoints = [...this.report.knowledgePoints];
      if (knowledgePoints.length > maxPoints) {
        // 按掌握程度排序，显示掌握程度最高和最低的知识点
        knowledgePoints.sort((a, b) => b.mastery_level - a.mastery_level);
        const topPoints = knowledgePoints.slice(0, maxPoints / 2);
        const bottomPoints = knowledgePoints.slice(-(maxPoints / 2));
        knowledgePoints = [...topPoints, ...bottomPoints];
      }

      const labels = knowledgePoints.map(kp => kp.name);
      const data = knowledgePoints.map(kp => Math.round(kp.mastery_level * 100)); // 四舍五入到整数

      this.radarChart = new Chart(ctx, {
        type: 'radar',
        data: {
          labels: labels,
          datasets: [{
            label: '掌握程度 (%)',
            data: data,
            backgroundColor: 'rgba(74, 108, 247, 0.2)',
            borderColor: '#4a6cf7',
            pointBackgroundColor: '#4a6cf7',
            pointBorderColor: '#fff',
            pointHoverBackgroundColor: '#fff',
            pointHoverBorderColor: '#4a6cf7'
          }]
        },
        options: {
          scale: {
            ticks: {
              beginAtZero: true,
              max: 100,
              stepSize: 20,
              callback: function(value) {
                return value + '%';
              }
            }
          },
          tooltips: {
            callbacks: {
              label: function(tooltipItem, data) {
                return `${data.labels[tooltipItem.index]}: ${tooltipItem.yLabel}%`;
              }
            }
          },
          legend: {
            position: 'top',
            labels: {
              fontColor: '#333',
              fontSize: 14
            }
          },
          responsive: true,
          maintainAspectRatio: false
        }
      });
    },
    viewCourse(courseId) {
      this.$router.push({ path: '/course/info', query: { id: courseId } });
    },
    refreshData() {
      // 显示加载状态
      this.loading = true;
      this.$toast.loading({
        message: '正在刷新数据...',
        forbidClick: true,
        duration: 0
      });

      // 初始化一个空的报告对象
      this.initEmptyReport();

      // 重新获取数据
      this.fetchReport()
        .then(() => {
          this.$toast.success('数据刷新成功');
        })
        .catch(err => {
          console.error('刷新数据失败:', err);
          this.$toast.fail('刷新数据失败');
        });
    },

    handleImageError(e, course) {
      // 当图片加载失败时，使用默认图片
      console.log(`课程图片加载失败: ${course.name}`, e);
      // 设置一个简单的占位符URL，而不是尝试导入不存在的图片
      e.target.src = 'data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%2060%2060%22%20preserveAspectRatio%3D%22none%22%3E%3Crect%20width%3D%2260%22%20height%3D%2260%22%20fill%3D%22%23EEEEEE%22%3E%3C%2Frect%3E%3Ctext%20x%3D%2214.5%22%20y%3D%2230%22%20style%3D%22fill%3A%23AAAAAA%3Bfont-weight%3Abold%3Bfont-family%3AArial%2C%20Helvetica%2C%20Open%20Sans%2C%20sans-serif%3Bfont-size%3A10px%22%20dominant-baseline%3D%22middle%22%20text-anchor%3D%22middle%22%3E课程%3C%2Ftext%3E%3C%2Fsvg%3E';
    }
  },
  mounted() {
    console.log('LearningReport组件挂载');

    // 检查是否已登录
    if (!localStorage.getItem('token_front')) {
      console.warn('用户未登录，无法获取数据');
      this.$toast.fail('请先登录');
      this.$router.replace('/auth/login');
      return;
    }

    // 直接获取数据，不使用延迟
    console.log('开始获取数据');

    // 初始化空数据
    this.initEmptyReport();

    // 显示加载状态
    this.loading = true;

    // 获取数据
    this.fetchReport();
  },
  beforeDestroy() {
    if (this.radarChart) {
      this.radarChart.destroy();
    }
  }
}
</script>

<style scoped>
.learning-report {
  background-color: #f5f7fa;
  min-height: 100vh;
}
.report-container {
  padding: 16px;
}
.section {
  margin-bottom: 24px;
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}
.overview-stats {
  display: flex;
  justify-content: space-around;
  margin-top: 16px;
}
.stat-item {
  text-align: center;
}
.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #4a6cf7;
}
.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
}
.radar-chart-container {
  height: 300px;
  margin-top: 16px;
  position: relative;
  z-index: 1;
}

.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.loading-text {
  margin-top: 10px;
  color: #333; /* 改为深灰色，更适合浅色背景 */
}
.suggestions {
  margin-top: 8px;
}
.suggestion {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  font-size: 14px;
}
.suggestion .van-icon {
  margin-right: 8px;
  margin-top: 2px;
  flex-shrink: 0;
}
.recommended-courses {
  margin-top: 16px;
}
.course-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #eee;
}
.course-item:last-child {
  border-bottom: none;
}
.course-info {
  flex: 1;
  margin-left: 12px;
  overflow: hidden;
}
.course-name {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.course-desc {
  font-size: 12px;
  color: #999;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.empty-state {
  padding: 20px;
  text-align: center;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.empty-tip {
  color: #999;
  font-size: 14px;
  margin-top: 10px;
}
</style>
