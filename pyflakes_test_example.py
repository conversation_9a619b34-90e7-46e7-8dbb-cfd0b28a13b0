#!/usr/bin/env python3
# 这个文件包含各种 Pyflakes 可以检测的错误

# 未使用的导入
import os
import sys
import json
import datetime
from collections import defaultdict
import requests
import random as rand

# 导入但未使用的变量
from math import pi, sin, cos

# 重复导入
import os

# 未定义的名称
def undefined_name_example():
    # 使用未定义的变量
    print(undefined_variable)
    return some_undefined_function()

# 重定义的名称
def redefined_name():
    x = 10
    print(x)
    x = 20  # 重定义变量

# 未使用的变量
def unused_variables():
    unused_var = "This variable is never used"
    used_var = "This one is used"
    print(used_var)
    
    # 未使用的循环变量
    for i in range(10):
        pass

# 未使用的函数参数
def unused_parameters(param1, param2, param3):
    print("Only using:", param1)
    # param2 和 param3 未使用

# 未使用的函数定义
def unused_function():
    """这个函数从未被调用"""
    return "Never called"

# 变量在定义之前使用
def used_before_defined():
    print(variable)  # 在定义之前使用
    variable = "Now defined"

# 全局变量未声明
def using_global_without_declaration():
    global_var = 10  # 局部变量
    
    def inner_function():
        print(global_var)  # 尝试使用外部作用域的变量，但没有使用 nonlocal

# 重复定义的函数
def duplicate_function():
    return "First definition"

def duplicate_function():
    return "Second definition"

# 字符串语法错误
def string_syntax_error():
    # 未闭合的字符串 - 修复为可解析的代码
    s = "This string is now closed"
    
    # 混合使用引号 - 修复为可解析的代码
    s2 = 'Mixed quotes'
    
    # 无效的转义序列
    s3 = "Invalid escape sequence \\z"

# 导入错误
def import_errors():
    # 循环导入
    from pyflakes_test_example import unused_function
    
    # 相对导入语法错误
    from ..module import something

# 未使用的异常变量
def unused_exception_variable():
    try:
        1/0
    except ZeroDivisionError as e:
        # e 未使用
        print("Division by zero")

# 不必要的 pass 语句
def unnecessary_pass():
    if True:
        print("True")
        pass  # 不必要的 pass

# 未使用的 with 语句变量
def unused_with_variable():
    with open("file.txt") as f:
        # f 未使用
        print("File opened")

# 未使用的 lambda 表达式
unused_lambda = lambda x, y: x + y

# 未使用的列表推导式
unused_list_comp = [x*2 for x in range(10)]

# 变量遮蔽内置函数
def shadowing_builtins():
    list = [1, 2, 3]  # 遮蔽内置的 list 函数
    print = "Not a function anymore"  # 遮蔽内置的 print 函数
    
# 星号导入
from os import *  # 星号导入

# 条件导入但未使用
if False:
    import numpy  # 条件导入但未使用

# 主程序
if __name__ == "__main__":
    # 一些实际使用的代码
    print("Running pyflakes test example")
    redefined_name()
    unused_variables()
    unused_parameters("used", "unused1", "unused2")
    
    # 尝试调用未定义的函数
    try:
        undefined_name_example()
    except NameError:
        print("Caught expected NameError")

