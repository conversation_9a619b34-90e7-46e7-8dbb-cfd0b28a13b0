from flask import Blueprint, request, jsonify
from myapp.models import (
    User, Admin, Course, Module, Scores, Comment,
    LearningActivity, LearningProgress, KnowledgePoint, LearningReport,
    db, course_knowledge_points, user_course_table
)
from myapp.front.auth import login_required
from myapp.admin.auth import admin_required
from myapp.api.resp import Resp
from datetime import datetime, timedelta
import json
from .utils import (
    generate_personal_report, generate_teaching_report,
    generate_learning_suggestions, recommend_courses
)
from myapp.data_analysis import data_analysis_bp

# 辅助函数：生成并保存个人学习报告
def generate_and_save_personal_report(user_id):
    """生成并保存个人学习报告"""
    try:
        # 获取用户信息
        user = User.query.get(user_id)
        if not user:
            print(f"用户 {user_id} 不存在")
            return False

        # 获取用户的课程
        courses = user.courses
        if not courses:
            print(f"用户 {user_id} 没有课程")
            return False

        # 获取用户的学习活动
        activities = LearningActivity.query.filter_by(user_id=user_id).all()

        # 计算总学习时长（小时）
        total_hours = sum(a.duration or 0 for a in activities) / 3600

        # 计算课程完成率
        total_courses = len(courses)
        completed_courses = []
        for activity in activities:
            if activity.activity_type == 'complete' and activity.course_id not in [c.id for c in completed_courses]:
                course = Course.query.get(activity.course_id)
                if course:
                    completed_courses.append(course)

        completion_rate = len(completed_courses) / total_courses * 100 if total_courses > 0 else 0

        # 获取知识点掌握情况
        progress_records = LearningProgress.query.filter_by(user_id=user_id).all()

        knowledge_points_data = []
        knowledge_points_dict = {}

        for record in progress_records:
            kp_id = record.knowledge_point_id

            if kp_id not in knowledge_points_dict:
                # 获取知识点信息
                kp = KnowledgePoint.query.get(kp_id)
                if not kp:
                    continue

                knowledge_points_dict[kp_id] = {
                    "name": kp.name,
                    "mastery_level": record.mastery_level,
                    "count": 1
                }
            else:
                # 如果已存在，计算平均掌握程度
                knowledge_points_dict[kp_id]["mastery_level"] += record.mastery_level
                knowledge_points_dict[kp_id]["count"] += 1

        # 计算平均掌握程度
        for kp_id, data in knowledge_points_dict.items():
            knowledge_points_data.append({
                "name": data["name"],
                "mastery_level": data["mastery_level"] / data["count"]
            })

        # 生成学习建议
        suggestions = []

        # 找出掌握程度最低的知识点
        if knowledge_points_data:
            sorted_kp = sorted(knowledge_points_data, key=lambda x: x["mastery_level"])
            weakest_kp = sorted_kp[0]
            suggestions.append(f"建议加强对{weakest_kp['name']}的学习")

            # 找出掌握程度最高的知识点
            strongest_kp = sorted_kp[-1]
            suggestions.append(f"您在{strongest_kp['name']}方面表现出色，可以考虑进一步深入学习")

            # 添加一个通用建议
            suggestions.append("建议制定合理的学习计划，保持学习的连续性")

        # 推荐课程
        # 获取用户未学习的课程
        all_courses = Course.query.all()
        user_course_ids = [c.id for c in courses]
        recommended_courses = []

        for course in all_courses:
            if course.id not in user_course_ids:
                recommended_courses.append({
                    "id": course.id,
                    "name": course.name,
                    "cover": course.cover,
                    "description": f"推荐理由：扩展您的技术栈，学习新领域"
                })

                if len(recommended_courses) >= 2:
                    break

        # 构建报告数据
        report_data = {
            "totalCourses": total_courses,
            "totalHours": round(total_hours, 1),
            "completionRate": round(completion_rate, 1),
            "knowledgePoints": knowledge_points_data,
            "suggestions": suggestions,
            "recommendedCourses": recommended_courses
        }

        # 保存报告到数据库
        new_report = LearningReport(
            user_id=user_id,
            report_type='personal',
            content=json.dumps(report_data)
        )
        db.session.add(new_report)
        db.session.commit()

        print(f"为用户 {user_id} 生成并保存了新的学习报告")
        return True
    except Exception as e:
        print(f"生成个人学习报告时出错: {e}")
        return False

# 辅助函数：生成并保存教师评估报告
def generate_and_save_teaching_report(admin_id):
    """生成并保存教师评估报告"""
    try:
        # 获取管理员信息
        admin = Admin.query.get(admin_id)
        if not admin:
            print(f"管理员 {admin_id} 不存在")
            return False

        # 获取管理员创建的课程
        courses = Course.query.filter_by(admin_id=admin_id).all()
        if not courses:
            print(f"管理员 {admin_id} 没有创建课程")
            return False

        # 基本统计数据
        total_courses = len(courses)
        course_ids = [course.id for course in courses]

        # 获取学习这些课程的学生数量
        student_ids = db.session.query(LearningActivity.user_id).filter(
            LearningActivity.course_id.in_(course_ids)
        ).distinct().all()
        student_ids = [s[0] for s in student_ids]
        total_students = len(student_ids)

        # 获取课程评分
        scores = Scores.query.filter(Scores.course_id.in_(course_ids)).all()

        # 计算平均评分
        total_score = sum(s.score for s in scores if s.score is not None)
        average_score = total_score / len(scores) if scores else 0

        # 课程参与度数据
        course_engagement = []
        for course in courses:
            views = LearningActivity.query.filter_by(course_id=course.id, activity_type='view').count()
            completions = LearningActivity.query.filter_by(course_id=course.id, activity_type='complete').count()

            course_engagement.append({
                'name': course.name,
                'views': views,
                'completions': completions
            })

        # 学生掌握情况
        student_mastery = {
            'excellent': 0,  # >80%
            'good': 0,       # 60-80%
            'average': 0,    # 40-60%
            'needImprovement': 0  # <40%
        }

        for student_id in student_ids:
            # 计算该学生对所有课程的平均掌握程度
            progress_records = LearningProgress.query.filter(
                LearningProgress.user_id == student_id,
                LearningProgress.course_id.in_(course_ids)
            ).all()

            if progress_records:
                avg_mastery = sum(p.mastery_level for p in progress_records) / len(progress_records)

                if avg_mastery > 0.8:
                    student_mastery['excellent'] += 1
                elif avg_mastery > 0.6:
                    student_mastery['good'] += 1
                elif avg_mastery > 0.4:
                    student_mastery['average'] += 1
                else:
                    student_mastery['needImprovement'] += 1

        # 评分分布
        score_distribution = {}
        for score in range(1, 6):  # 假设评分是1-5
            count = len([s for s in scores if s.score == score])
            score_distribution[str(score)] = count

        # 生成教学建议
        suggestions = []

        # 根据课程完成率生成建议
        for course in courses:
            views = LearningActivity.query.filter_by(course_id=course.id, activity_type='view').count()
            completions = LearningActivity.query.filter_by(course_id=course.id, activity_type='complete').count()

            completion_rate = completions / views * 100 if views > 0 else 0

            if completion_rate < 50:
                suggestions.append({
                    'title': f"提高{course.name}的完成率",
                    'content': f"该课程的完成率较低，建议优化课程内容，增加互动环节，提高学生参与度"
                })
            elif completion_rate >= 80:
                suggestions.append({
                    'title': f"继续保持{course.name}的教学质量",
                    'content': f"该课程完成率高，反馈良好，建议保持现有教学方法并定期更新内容"
                })

        # 如果建议不足3条，添加通用建议
        if len(suggestions) < 3:
            suggestions.append({
                'title': "增加实践案例",
                'content': "建议在课程中增加更多实际应用案例，提高学生兴趣和参与度"
            })

        if len(suggestions) < 3:
            suggestions.append({
                'title': "优化课程难度曲线",
                'content': "建议调整课程难度曲线，确保学生能够循序渐进地掌握知识点"
            })

        # 构建报告数据
        report_data = {
            'totalCourses': total_courses,
            'totalStudents': total_students,
            'averageScore': round(average_score, 1),
            'courseEngagement': course_engagement,
            'studentMastery': student_mastery,
            'scoreDistribution': score_distribution,
            'suggestions': suggestions[:3]  # 最多返回3条建议
        }

        # 保存报告到数据库
        new_report = LearningReport(
            user_id=admin_id,
            report_type='teaching',
            content=json.dumps(report_data)
        )
        db.session.add(new_report)
        db.session.commit()

        print(f"为管理员 {admin_id} 生成并保存了新的教学评估报告")
        return True
    except Exception as e:
        print(f"生成教师评估报告时出错: {e}")
        return False

# 数据收集API
@data_analysis_bp.route("/activity/record", methods=["POST"])
@login_required
def record_activity(userid):
    """记录学习活动"""
    data = request.get_json()
    activity_type = data.get('activity_type')
    course_id = data.get('course_id')

    activity = LearningActivity(
        user_id=userid,
        course_id=course_id,
        activity_type=activity_type,
        start_time=datetime.fromisoformat(data.get('start_time').replace('Z', '+00:00')),
        end_time=None,
        duration=data.get('duration', 0)
    )
    db.session.add(activity)
    db.session.commit()

    # 如果是完成课程的活动，更新知识点掌握程度和生成新的学习报告
    if activity_type == 'complete':
        try:
            # 获取课程关联的知识点
            knowledge_points = KnowledgePoint.query.join(
                course_knowledge_points
            ).filter(
                course_knowledge_points.c.course_id == course_id
            ).all()

            # 课程完成时，大幅提升知识点掌握程度（提升30%）
            improvement = 0.3

            for kp in knowledge_points:
                progress = LearningProgress.query.filter_by(
                    user_id=userid,
                    course_id=course_id,
                    knowledge_point_id=kp.id
                ).first()

                if progress:
                    # 已有记录，更新掌握程度（最高为1.0）
                    progress.mastery_level = min(1.0, progress.mastery_level + improvement)
                    progress.last_updated = datetime.now()
                    print(f"课程完成：更新知识点 {kp.id} ({kp.name}) 的掌握程度为: {progress.mastery_level}")
                else:
                    # 创建新记录
                    progress = LearningProgress(
                        user_id=userid,
                        course_id=course_id,
                        knowledge_point_id=kp.id,
                        mastery_level=0.5,  # 完成课程但没有之前的记录，设置为中等掌握程度
                        last_updated=datetime.now()
                    )
                    db.session.add(progress)
                    print(f"课程完成：创建知识点 {kp.id} ({kp.name}) 的掌握程度记录: 0.5")

            db.session.commit()

            # 生成新的个人学习报告
            generate_and_save_personal_report(userid)

            print(f"用户 {userid} 完成课程 {course_id}，更新知识点掌握程度和学习报告")
        except Exception as e:
            print(f"课程完成后更新数据时出错: {e}")
            # 不要让这个错误影响整个流程

    return Resp.success(data={"id": activity.id})

@data_analysis_bp.route("/activity/update/<int:activity_id>", methods=["PUT"])
@login_required
def update_activity(userid, activity_id):
    """更新学习活动"""
    activity = LearningActivity.query.get(activity_id)
    if not activity or activity.user_id != int(userid):
        return Resp.error(message="活动记录不存在或无权限")

    data = request.get_json()
    if 'end_time' in data:
        activity.end_time = datetime.fromisoformat(data.get('end_time').replace('Z', '+00:00'))
    if 'duration' in data:
        activity.duration = data.get('duration')

    db.session.commit()

    # 如果是学习活动，并且学习时长超过一定阈值，更新知识点掌握程度
    if activity.activity_type == 'learning' and activity.duration and activity.duration > 300:  # 超过5分钟
        try:
            # 获取课程关联的知识点
            knowledge_points = KnowledgePoint.query.join(
                course_knowledge_points
            ).filter(
                course_knowledge_points.c.course_id == activity.course_id
            ).all()

            # 计算学习时长对掌握程度的影响（学习时间越长，提升越多，但有上限）
            # 每30分钟学习时间提升5%的掌握程度，最多提升20%
            improvement = min(0.2, (activity.duration / 1800) * 0.05)

            for kp in knowledge_points:
                progress = LearningProgress.query.filter_by(
                    user_id=userid,
                    course_id=activity.course_id,
                    knowledge_point_id=kp.id
                ).first()

                if progress:
                    # 已有记录，更新掌握程度（最高为1.0）
                    progress.mastery_level = min(1.0, progress.mastery_level + improvement)
                    progress.last_updated = datetime.now()
                    print(f"更新知识点 {kp.id} ({kp.name}) 的掌握程度为: {progress.mastery_level}")
                else:
                    # 创建新记录
                    progress = LearningProgress(
                        user_id=userid,
                        course_id=activity.course_id,
                        knowledge_point_id=kp.id,
                        mastery_level=improvement,  # 初始掌握程度
                        last_updated=datetime.now()
                    )
                    db.session.add(progress)
                    print(f"创建知识点 {kp.id} ({kp.name}) 的掌握程度记录: {improvement}")

            db.session.commit()
            print(f"用户 {userid} 完成学习活动，更新知识点掌握程度")

            # 如果学习时长超过30分钟，更新学习报告
            if activity.duration > 1800:  # 超过30分钟
                # 生成新的个人学习报告
                generate_and_save_personal_report(userid)
                print(f"用户 {userid} 学习时长超过30分钟，更新学习报告")
        except Exception as e:
            print(f"更新知识点掌握程度时出错: {e}")
            # 不要让这个错误影响整个流程

    return Resp.success()

# 学习效果评估API
@data_analysis_bp.route("/progress/<int:course_id>")
@login_required
def get_learning_progress(userid, course_id):
    """获取学习进度"""
    progress = LearningProgress.query.filter_by(user_id=userid, course_id=course_id).all()
    return Resp.success(data=[{
        "knowledge_point": p.knowledge_point.name,
        "mastery_level": p.mastery_level,
        "last_updated": p.last_updated
    } for p in progress])

# 知识点掌握评估API
@data_analysis_bp.route("/knowledge_points/mastery")
@login_required
def get_knowledge_points_mastery(userid):
    """获取知识点掌握情况"""
    try:
        print(f"用户{userid}请求获取知识点掌握情况")

        # 从数据库获取用户的学习进度数据
        progress_records = LearningProgress.query.filter_by(user_id=userid).all()

        if not progress_records:
            print(f"用户{userid}没有学习进度记录")
            return Resp.success(data=[])

        # 整理知识点掌握情况
        knowledge_points_data = []

        # 使用字典去重，同一知识点可能在不同课程中出现
        knowledge_points_dict = {}

        for record in progress_records:
            kp_id = record.knowledge_point_id

            if kp_id not in knowledge_points_dict:
                # 获取知识点信息
                kp = KnowledgePoint.query.get(kp_id)
                if not kp:
                    continue

                knowledge_points_dict[kp_id] = {
                    "name": kp.name,
                    "mastery_level": record.mastery_level,
                    "count": 1
                }
            else:
                # 如果已存在，计算平均掌握程度
                knowledge_points_dict[kp_id]["mastery_level"] += record.mastery_level
                knowledge_points_dict[kp_id]["count"] += 1

        # 计算平均掌握程度
        for kp_id, data in knowledge_points_dict.items():
            knowledge_points_data.append({
                "name": data["name"],
                "mastery_level": data["mastery_level"] / data["count"]
            })

        print(f"返回用户{userid}的知识点掌握情况，共{len(knowledge_points_data)}条记录")
        return Resp.success(data=knowledge_points_data)
    except Exception as e:
        print(f"获取知识点掌握情况时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return Resp.error(message="获取知识点掌握情况失败")

# 学习进步趋势分析API
@data_analysis_bp.route("/progress/trend")
@login_required
def get_progress_trend(userid):
    """获取学习进步趋势"""
    try:
        print(f"用户{userid}请求获取学习趋势")

        # 从数据库获取用户的学习活动数据
        activities = LearningActivity.query.filter_by(user_id=userid).all()

        if not activities:
            print(f"用户{userid}没有学习活动记录")
            return Resp.success(data=[])

        # 获取过去30天的日期范围
        today = datetime.now()
        start_date = today - timedelta(days=29)

        # 初始化日期字典，确保每一天都有数据
        date_dict = {}
        for i in range(30):
            date = start_date + timedelta(days=i)
            date_str = date.strftime('%Y-%m-%d')
            date_dict[date_str] = {
                "date": date_str,
                "count": 0,
                "duration": 0
            }

        # 统计每天的学习次数和时长
        for activity in activities:
            # 只统计学习类型的活动
            if activity.activity_type != 'learning':
                continue

            # 获取活动日期
            activity_date = activity.start_time.strftime('%Y-%m-%d')

            # 如果日期在我们的统计范围内
            if activity_date in date_dict:
                date_dict[activity_date]["count"] += 1
                date_dict[activity_date]["duration"] += activity.duration or 0

        # 转换为列表
        trend_data = list(date_dict.values())

        # 按日期排序
        trend_data.sort(key=lambda x: x["date"])

        print(f"返回用户{userid}的学习趋势，共{len(trend_data)}天的数据")
        return Resp.success(data=trend_data)
    except Exception as e:
        print(f"获取学习趋势时发生错误: {e}")
        import traceback
        traceback.print_exc()
        # 返回一个空的数组，而不是错误信息
        return Resp.success(data=[])

# 个性化学习报告API
@data_analysis_bp.route("/report/personal")
@login_required
def get_personal_report(userid):
    """获取个性化学习报告"""
    try:
        print(f"用户{userid}请求获取个人学习报告")

        # 从数据库获取用户的学习报告
        report = LearningReport.query.filter_by(user_id=userid, report_type='personal').order_by(LearningReport.created_at.desc()).first()

        # 检查报告是否存在且是否是最近12小时内生成的
        if report and (datetime.now() - report.created_at).total_seconds() < 43200:  # 12小时 = 43200秒
            # 如果数据库中有最近12小时内生成的学习报告，直接返回
            print(f"从数据库获取到用户{userid}的最近学习报告")
            try:
                report_data = json.loads(report.content)
                return Resp.success(data=report_data)
            except json.JSONDecodeError:
                print(f"学习报告内容不是有效的JSON格式: {report.content[:100]}...")

        # 如果没有找到报告、报告内容无效或报告已过期，则生成新的报告
        print(f"为用户{userid}生成新的学习报告")

        # 使用辅助函数生成并保存个人学习报告
        if generate_and_save_personal_report(userid):
            # 获取新生成的报告
            new_report = LearningReport.query.filter_by(user_id=userid, report_type='personal').order_by(LearningReport.created_at.desc()).first()
            if new_report:
                report_data = json.loads(new_report.content)
                return Resp.success(data=report_data)
            else:
                return Resp.error(message="生成报告失败")
        else:
            return Resp.error(message="生成报告失败")
    except Exception as e:
        print(f"获取学习报告时发生错误: {e}")
        import traceback
        traceback.print_exc()
        # 返回一个错误响应，而不是让请求挂起
        return Resp.error(message="获取学习报告时发生错误")

# 教学效果评估报告API（仅管理员可用）
@data_analysis_bp.route("/report/teaching")
@admin_required
def get_teaching_report(adminid):
    """获取教学效果评估报告"""
    try:
        print(f"管理员{adminid}请求获取教学效果评估报告")

        # 从数据库获取管理员的学习报告
        report = LearningReport.query.filter_by(user_id=adminid, report_type='teaching').order_by(LearningReport.created_at.desc()).first()

        # 检查报告是否存在且是否是最近24小时内生成的
        if report and (datetime.now() - report.created_at).total_seconds() < 86400:  # 24小时 = 86400秒
            # 如果数据库中有最近24小时内生成的教学报告，直接返回
            print(f"从数据库获取到管理员{adminid}的最近教学报告")
            try:
                report_data = json.loads(report.content)
                return Resp.success(data=report_data)
            except json.JSONDecodeError:
                print(f"教学报告内容不是有效的JSON格式: {report.content[:100]}...")

        # 如果没有找到报告、报告内容无效或报告已过期，则生成新的报告
        print(f"为管理员{adminid}生成新的教学报告")

        # 使用辅助函数生成并保存教师评估报告
        if generate_and_save_teaching_report(adminid):
            # 获取新生成的报告
            new_report = LearningReport.query.filter_by(user_id=adminid, report_type='teaching').order_by(LearningReport.created_at.desc()).first()
            if new_report:
                report_data = json.loads(new_report.content)
                return Resp.success(data=report_data)
            else:
                return Resp.error(message="生成报告失败")
        else:
            return Resp.error(message="生成报告失败")
    except Exception as e:
        print(f"获取教学报告时发生错误: {e}")
        import traceback
        traceback.print_exc()
        # 返回一个错误响应，而不是让请求挂起
        return Resp.error(message="获取教学报告时发生错误")

# 知识点库管理API（仅管理员可用）
@data_analysis_bp.route("/knowledge_points", methods=["GET"])
@admin_required
def get_knowledge_points(adminid):
    """获取知识点列表"""
    try:
        print(f"管理员{adminid}请求获取知识点列表")
        knowledge_points = KnowledgePoint.query.all()
        print(f"查询到{len(knowledge_points)}个知识点")

        result = [{
            "id": kp.id,
            "name": kp.name,
            "description": kp.description,
            "module": {
                "id": kp.module.id,
                "name": kp.module.name
            } if kp.module else None,
            "created_at": kp.created_at
        } for kp in knowledge_points]

        print(f"返回知识点数据: {result[:2]}...")  # 只打印前两个，避免日志过长
        return Resp.success(data=result)
    except Exception as e:
        print(f"获取知识点列表时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return Resp.error(message=f"获取知识点列表失败: {str(e)}")

@data_analysis_bp.route("/knowledge_points", methods=["POST"])
@admin_required
def add_knowledge_point(adminid):
    """添加知识点"""
    try:
        print(f"管理员{adminid}尝试添加知识点")
        data = request.get_json()
        print(f"接收到的数据: {data}")

        # 验证必要字段
        if not data.get('name'):
            return Resp.error(message="知识点名称不能为空")

        # 创建知识点
        kp = KnowledgePoint(
            name=data.get('name'),
            description=data.get('description', ''),
            module_id=data.get('module_id')
        )

        print(f"准备添加知识点: 名称={kp.name}, 描述={kp.description}, 模块ID={kp.module_id}")
        db.session.add(kp)
        db.session.commit()

        # 重新查询以获取完整数据
        kp = KnowledgePoint.query.get(kp.id)

        result = {
            "id": kp.id,
            "name": kp.name,
            "description": kp.description,
            "module": {
                "id": kp.module.id,
                "name": kp.module.name
            } if kp.module else None,
            "created_at": kp.created_at
        }

        print(f"知识点添加成功: {result}")
        return Resp.success(data=result)
    except Exception as e:
        db.session.rollback()
        print(f"添加知识点时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return Resp.error(message=f"添加知识点失败: {str(e)}")

@data_analysis_bp.route("/knowledge_points/<int:kp_id>", methods=["PUT"])
@admin_required
def update_knowledge_point(adminid, kp_id):
    """更新知识点"""
    try:
        print(f"管理员{adminid}尝试更新知识点ID: {kp_id}")
        kp = KnowledgePoint.query.get(kp_id)

        if not kp:
            print(f"知识点不存在")
            return Resp.error(message="知识点不存在")

        data = request.get_json()
        print(f"接收到的数据: {data}")

        # 验证必要字段
        if not data.get('name'):
            return Resp.error(message="知识点名称不能为空")

        # 更新知识点
        old_name = kp.name
        old_module_id = kp.module_id

        kp.name = data.get('name', kp.name)
        kp.description = data.get('description', kp.description)
        kp.module_id = data.get('module_id', kp.module_id)

        print(f"更新知识点: ID={kp.id}, 名称从'{old_name}'变为'{kp.name}', 模块ID从{old_module_id}变为{kp.module_id}")
        db.session.commit()

        # 重新查询以获取完整数据
        kp = KnowledgePoint.query.get(kp_id)

        result = {
            "id": kp.id,
            "name": kp.name,
            "description": kp.description,
            "module": {
                "id": kp.module.id,
                "name": kp.module.name
            } if kp.module else None,
            "created_at": kp.created_at
        }

        print(f"知识点更新成功: {result}")
        return Resp.success(data=result)
    except Exception as e:
        db.session.rollback()
        print(f"更新知识点时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return Resp.error(message=f"更新知识点失败: {str(e)}")

@data_analysis_bp.route("/knowledge_points/<int:kp_id>", methods=["DELETE"])
@admin_required
def delete_knowledge_point(adminid, kp_id):
    """删除知识点"""
    try:
        print(f"尝试删除知识点ID: {kp_id}, 管理员ID: {adminid}")
        kp = KnowledgePoint.query.get(kp_id)

        if not kp:
            print(f"知识点不存在")
            return Resp.error(message="知识点不存在")

        # 先删除与知识点相关的学习进度记录
        from myapp.models import LearningProgress
        progress_records = LearningProgress.query.filter_by(knowledge_point_id=kp_id).all()
        print(f"删除{len(progress_records)}条学习进度记录")
        for progress in progress_records:
            db.session.delete(progress)

        # 删除课程与知识点的关联
        print(f"删除课程与知识点的关联")
        stmt = course_knowledge_points.delete().where(
            course_knowledge_points.c.knowledge_point_id == kp_id
        )
        db.session.execute(stmt)

        # 最后删除知识点
        print(f"删除知识点: {kp.name}")
        db.session.delete(kp)
        db.session.commit()

        return Resp.success(message="知识点删除成功")
    except Exception as e:
        db.session.rollback()
        print(f"删除知识点时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return Resp.error(message=f"删除知识点失败: {str(e)}")

# 关联课程和知识点API
@data_analysis_bp.route("/course/<int:course_id>/knowledge_points", methods=["GET"])
@admin_required
def get_course_knowledge_points(adminid, course_id):
    """获取课程关联的知识点"""
    try:
        print(f"管理员{adminid}请求获取课程{course_id}的知识点")

        course = Course.query.get(course_id)
        if not course:
            print(f"课程{course_id}不存在")
            return Resp.error(message="课程不存在")

        knowledge_points = KnowledgePoint.query.join(
            course_knowledge_points
        ).filter(
            course_knowledge_points.c.course_id == course_id
        ).all()

        print(f"查询到课程{course_id}关联的知识点数量: {len(knowledge_points)}")

        result = [{
            "id": kp.id,
            "name": kp.name,
            "description": kp.description
        } for kp in knowledge_points]

        return Resp.success(data=result)
    except Exception as e:
        print(f"获取课程知识点时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return Resp.error(message=f"获取课程知识点失败: {str(e)}")

@data_analysis_bp.route("/course/<int:course_id>/knowledge_points", methods=["POST"])
@admin_required
def add_course_knowledge_point(adminid, course_id):
    """为课程添加知识点"""
    try:
        print(f"管理员{adminid}尝试为课程{course_id}添加知识点")

        course = Course.query.get(course_id)
        if not course:
            print(f"课程{course_id}不存在")
            return Resp.error(message="课程不存在")

        data = request.get_json()
        print(f"接收到的数据: {data}")

        knowledge_point_id = data.get('knowledge_point_id')
        if not knowledge_point_id:
            return Resp.error(message="知识点ID不能为空")

        kp = KnowledgePoint.query.get(knowledge_point_id)
        if not kp:
            print(f"知识点{knowledge_point_id}不存在")
            return Resp.error(message="知识点不存在")

        # 检查是否已关联 - 使用新版SQLAlchemy语法
        print(f"检查课程{course_id}和知识点{knowledge_point_id}是否已关联")

        # 使用直接查询而不是select语句
        existing = db.session.query(course_knowledge_points).filter(
            course_knowledge_points.c.course_id == course_id,
            course_knowledge_points.c.knowledge_point_id == knowledge_point_id
        ).first()

        if existing:
            print(f"课程{course_id}和知识点{knowledge_point_id}已关联")
            return Resp.error(message="该知识点已关联到课程")

        # 添加关联
        print(f"添加课程{course_id}和知识点{knowledge_point_id}的关联")
        stmt = course_knowledge_points.insert().values(
            course_id=course_id,
            knowledge_point_id=knowledge_point_id
        )
        db.session.execute(stmt)
        db.session.commit()

        print(f"课程{course_id}和知识点{knowledge_point_id}关联成功")
        return Resp.success(message="知识点关联成功")
    except Exception as e:
        db.session.rollback()
        print(f"为课程添加知识点时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return Resp.error(message=f"为课程添加知识点失败: {str(e)}")

@data_analysis_bp.route("/course/<int:course_id>/knowledge_points/<int:kp_id>", methods=["DELETE"])
@admin_required
def remove_course_knowledge_point(adminid, course_id, kp_id):
    """移除课程关联的知识点"""
    try:
        print(f"管理员{adminid}尝试移除课程{course_id}和知识点{kp_id}的关联")

        course = Course.query.get(course_id)
        if not course:
            print(f"课程{course_id}不存在")
            return Resp.error(message="课程不存在")

        kp = KnowledgePoint.query.get(kp_id)
        if not kp:
            print(f"知识点{kp_id}不存在")
            return Resp.error(message="知识点不存在")

        # 检查关联是否存在
        existing = db.session.query(course_knowledge_points).filter(
            course_knowledge_points.c.course_id == course_id,
            course_knowledge_points.c.knowledge_point_id == kp_id
        ).first()

        if not existing:
            print(f"课程{course_id}和知识点{kp_id}没有关联")
            return Resp.error(message="该课程未关联此知识点")

        # 移除关联
        print(f"移除课程{course_id}和知识点{kp_id}的关联")
        stmt = course_knowledge_points.delete().where(
            db.and_(
                course_knowledge_points.c.course_id == course_id,
                course_knowledge_points.c.knowledge_point_id == kp_id
            )
        )
        db.session.execute(stmt)
        db.session.commit()

        print(f"课程{course_id}和知识点{kp_id}的关联已移除")
        return Resp.success(message="知识点关联已移除")
    except Exception as e:
        db.session.rollback()
        print(f"移除课程知识点关联时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return Resp.error(message=f"移除课程知识点关联失败: {str(e)}")
