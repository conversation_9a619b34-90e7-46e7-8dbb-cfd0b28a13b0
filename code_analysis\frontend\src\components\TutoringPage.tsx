import React, { useState, useRef, useEffect } from 'react';
import '../styles/TutoringPage.css';
import { <PERSON>a<PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON>, FaPaperPlane, FaSpinner } from 'react-icons/fa';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { tomorrow } from 'react-syntax-highlighter/dist/esm/styles/prism';

interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

const TutoringPage: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [topic, setTopic] = useState<string>('');
  const [difficulty, setDifficulty] = useState<string>('');

  // 自动滚动到最新消息
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // 初始欢迎消息
  useEffect(() => {
    const welcomeMessage: Message = {
      id: Date.now().toString(),
      type: 'assistant',
      content: '你好！我是你的编程辅导助手。有任何编程问题都可以问我，我会尽力帮助你解决问题。',
      timestamp: new Date()
    };
    setMessages([welcomeMessage]);
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim()) return;

    // 添加用户消息
    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: input,
      timestamp: new Date()
    };
    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);

    try {
      // 调用API
      const response = await fetch('/api/tutoring/ask', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          question: input
        })
      });

      const data = await response.json();

      if (data.status === 'success') {
        // 添加助手回复
        const assistantMessage: Message = {
          id: Date.now().toString(),
          type: 'assistant',
          content: data.answer,
          timestamp: new Date()
        };
        setMessages(prev => [...prev, assistantMessage]);
        
        // 更新主题和难度
        if (data.understanding) {
          setTopic(data.understanding.topic);
          setDifficulty(data.understanding.difficulty);
        }
      } else {
        // 添加错误消息
        const errorMessage: Message = {
          id: Date.now().toString(),
          type: 'assistant',
          content: `抱歉，我遇到了一些问题：${data.message || '未知错误'}`,
          timestamp: new Date()
        };
        setMessages(prev => [...prev, errorMessage]);
      }
    } catch (error) {
      console.error('Error:', error);
      // 添加错误消息
      const errorMessage: Message = {
        id: Date.now().toString(),
        type: 'assistant',
        content: '抱歉，发生了网络错误。请稍后再试。',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  // 自定义渲染代码块
  const components = {
    code({ node, inline, className, children, ...props }: any) {
      const match = /language-(\w+)/.exec(className || '');
      return !inline && match ? (
        <SyntaxHighlighter
          style={tomorrow}
          language={match[1]}
          PreTag="div"
          {...props}
        >
          {String(children).replace(/\n$/, '')}
        </SyntaxHighlighter>
      ) : (
        <code className={className} {...props}>
          {children}
        </code>
      );
    }
  };

  return (
    <div className="tutoring-container">
      <div className="tutoring-header">
        <h1>智能编程辅导</h1>
        {topic && difficulty && (
          <div className="tutoring-info">
            <span className="topic-badge">主题: {topic}</span>
            <span className={`difficulty-badge ${difficulty.toLowerCase()}`}>
              难度: {difficulty}
            </span>
          </div>
        )}
      </div>
      
      <div className="messages-container">
        {messages.map(message => (
          <div 
            key={message.id} 
            className={`message ${message.type === 'user' ? 'user-message' : 'assistant-message'}`}
          >
            <div className="message-avatar">
              {message.type === 'user' ? <FaUser /> : <FaRobot />}
            </div>
            <div className="message-content">
              <ReactMarkdown components={components}>
                {message.content}
              </ReactMarkdown>
              <div className="message-timestamp">
                {message.timestamp.toLocaleTimeString()}
              </div>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
        
        {isLoading && (
          <div className="loading-indicator">
            <FaSpinner className="spinner" />
            <span>思考中...</span>
          </div>
        )}
      </div>
      
      <form className="input-form" onSubmit={handleSubmit}>
        <textarea
          value={input}
          onChange={e => setInput(e.target.value)}
          placeholder="输入你的编程问题..."
          disabled={isLoading}
          onKeyDown={e => {
            if (e.key === 'Enter' && !e.shiftKey) {
              e.preventDefault();
              handleSubmit(e);
            }
          }}
        />
        <button type="submit" disabled={isLoading || !input.trim()}>
          {isLoading ? <FaSpinner className="spinner" /> : <FaPaperPlane />}
        </button>
      </form>
      
      <div className="tutoring-footer">
        <p>提示: 你可以提问关于编程语言、算法、调试问题等任何编程相关的问题。</p>
      </div>
    </div>
  );
};

export default TutoringPage;
