import os
import sys
import subprocess
import threading
import time
from flask import render_template, jsonify, request, redirect, url_for, current_app
from . import code_analysis_bp
from myapp.front.auth import login_required
from myapp.api.resp import Resp
import requests
import json
from pathlib import Path

# 全局变量，用于存储FastAPI进程
fastapi_process = None
fastapi_port = 8000  # FastAPI服务的端口

def start_fastapi_server():
    """启动FastAPI服务器"""
    global fastapi_process

    # 如果已经有进程在运行，先停止它
    if fastapi_process is not None:
        try:
            fastapi_process.terminate()
            fastapi_process.wait()
        except:
            pass

    # 获取code_analysis目录的路径
    code_analysis_dir = os.path.join(current_app.root_path, '..', 'code_analysis')
    code_analysis_dir = os.path.abspath(code_analysis_dir)

    print(f"代码分析目录: {code_analysis_dir}")

    # 检查目录是否存在
    if not os.path.exists(code_analysis_dir):
        print(f"错误: 代码分析目录不存在: {code_analysis_dir}")
        return False

    # 检查backend/app.py是否存在
    app_path = os.path.join(code_analysis_dir, 'backend', 'app.py')
    if not os.path.exists(app_path):
        print(f"错误: FastAPI应用文件不存在: {app_path}")
        return False

    # 使用专门的启动脚本
    run_script = os.path.join(code_analysis_dir, 'run_fastapi.py')

    # 检查启动脚本是否存在
    if not os.path.exists(run_script):
        print(f"错误: 启动脚本不存在: {run_script}")
        return False

    cmd = [
        sys.executable,
        run_script,
        str(fastapi_port)
    ]

    print(f"执行命令: {' '.join(cmd)}")

    # 设置环境变量，确保Python能找到正确的模块
    env = os.environ.copy()
    env["PYTHONPATH"] = code_analysis_dir

    # 设置工作目录为code_analysis
    try:
        fastapi_process = subprocess.Popen(
            cmd,
            cwd=code_analysis_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            env=env
        )

        # 等待服务启动
        time.sleep(5)  # 增加等待时间

        # 检查进程是否还在运行
        if fastapi_process.poll() is not None:
            # 进程已经结束，获取错误输出
            stdout, stderr = fastapi_process.communicate()
            print(f"FastAPI服务启动失败，进程已退出")
            print(f"标准输出: {stdout}")
            print(f"错误输出: {stderr}")
            return False

        # 检查服务是否可访问
        max_retries = 3
        for i in range(max_retries):
            try:
                response = requests.get(f"http://127.0.0.1:{fastapi_port}/health", timeout=2)
                if response.status_code == 200:
                    print(f"FastAPI服务已启动，运行在端口 {fastapi_port}")
                    return True
            except requests.exceptions.RequestException as e:
                print(f"尝试 {i+1}/{max_retries} 连接FastAPI服务失败: {str(e)}")
                if i < max_retries - 1:  # 如果不是最后一次尝试
                    time.sleep(2)  # 等待后再次尝试

        # 如果所有尝试都失败，尝试直接访问根路径
        try:
            response = requests.get(f"http://127.0.0.1:{fastapi_port}/", timeout=2)
            if response.status_code == 200:
                print(f"FastAPI服务已启动（根路径可访问），运行在端口 {fastapi_port}")
                return True
        except:
            pass

        print("FastAPI服务启动失败，无法连接到服务")
        return False
    except Exception as e:
        print(f"启动FastAPI服务时发生异常: {str(e)}")
        return False

def stop_fastapi_server():
    """停止FastAPI服务器"""
    global fastapi_process
    if fastapi_process is not None:
        fastapi_process.terminate()
        fastapi_process.wait()
        fastapi_process = None
        print("FastAPI服务已停止")

@code_analysis_bp.route('/')
@login_required
def index(userid):
    """代码分析主页"""
    # 检查服务是否已经在运行
    try:
        response = requests.get(f"http://127.0.0.1:{fastapi_port}/health", timeout=2)
        if response.status_code == 200:
            print(f"FastAPI服务已经在运行，端口: {fastapi_port}")
            return Resp.success(data={
                "service_url": f"http://127.0.0.1:{fastapi_port}/ui",
                "api_url": f"http://127.0.0.1:{fastapi_port}/api"
            })
    except:
        pass

    # 服务未运行，尝试启动
    if not start_fastapi_server():
        # 启动失败，返回错误信息
        return Resp.error(message="无法启动代码分析服务，请检查日志获取更多信息")

    # 返回代码分析服务的URL
    return Resp.success(data={
        "service_url": f"http://127.0.0.1:{fastapi_port}/ui",
        "api_url": f"http://127.0.0.1:{fastapi_port}/api"
    })

@code_analysis_bp.route('/status')
@login_required
def status(userid):
    """检查代码分析服务状态"""
    global fastapi_process

    if fastapi_process is None:
        return Resp.error(message="代码分析服务未启动")

    # 检查进程是否还在运行
    if fastapi_process.poll() is not None:
        return Resp.error(message="代码分析服务已停止")

    # 检查服务是否可访问
    try:
        response = requests.get(f"http://127.0.0.1:{fastapi_port}/health")
        if response.status_code == 200:
            return Resp.success(data={"status": "running"})
    except:
        pass

    return Resp.error(message="代码分析服务不可访问")

@code_analysis_bp.route('/stop')
@login_required
def stop(userid):
    """停止代码分析服务"""
    stop_fastapi_server()
    return Resp.success(message="代码分析服务已停止")

# 当应用关闭时，确保FastAPI服务也被关闭
def cleanup():
    stop_fastapi_server()
