import java.io.File;
import java.io.FileInputStream;
import java.io.ObjectInputStream;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.Statement;

public class VulnerableJavaExample {

    // SQL 注入漏洞示例
    public void sqlInjectionExample(String userId) {
        try {
            Connection conn = DriverManager.getConnection("********************************", "user", "password");
            Statement stmt = conn.createStatement();
            
            // 不安全的 SQL 查询构建，容易受到 SQL 注入攻击
            String query = "SELECT * FROM users WHERE id = " + userId;
            stmt.executeQuery(query);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // 命令注入漏洞示例
    public void commandInjectionExample(String userInput) {
        try {
            // 直接执行用户输入的命令，存在命令注入风险
            Runtime.getRuntime().exec("ping " + userInput);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // 不安全的反序列化示例
    public Object unsafeDeserialization(String filePath) {
        try {
            FileInputStream fileIn = new FileInputStream(filePath);
            ObjectInputStream in = new ObjectInputStream(fileIn);
            
            // 不安全的反序列化，可能导致远程代码执行
            Object obj = in.readObject();
            in.close();
            fileIn.close();
            return obj;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    // 不安全的文件操作示例
    public boolean deleteFile(String filePath) {
        // 未验证的文件路径，可能导致任意文件删除
        File file = new File(filePath);
        return file.delete();
    }

    // 硬编码的密码示例
    private static final String DB_PASSWORD = "admin123";
    
    public Connection getDatabaseConnection() {
        try {
            // 使用硬编码的密码
            return DriverManager.getConnection(
                "********************************", 
                "admin", 
                DB_PASSWORD
            );
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    // 不安全的异常处理示例
    public void processData(String data) {
        try {
            // 处理数据的代码
            if (data.length() > 100) {
                throw new IllegalArgumentException("Data too long");
            }
        } catch (Exception e) {
            // 不安全的错误信息处理，可能泄露敏感信息
            System.out.println("Error processing data: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
