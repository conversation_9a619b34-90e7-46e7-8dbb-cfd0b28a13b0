from core.analyzers.context_analyzer import Context<PERSON><PERSON>yzer
from code_analyzer import Ana<PERSON><PERSON><PERSON>onfig

def analyze_single_file():
    """单文件分析示例"""
    config = AnalyzerConfig()
    analyzer = ContextAnalyzer(config)
    
    # 分析单个文件
    analyzer.analyze_project_context(['example.py'])
    
    # 获取分析结果
    context = analyzer.get_file_context('example.py')
    print("文件分析结果:", context)
    
    # 获取函数调用图
    call_graph = analyzer.get_call_graph('main')
    print("\n=== 函数调用图 ===")
    print(f"函数名: {call_graph['name']}")
    print(f"调用的函数: {list(call_graph['calls'])}")
    print(f"被以下函数调用: {list(call_graph['called_by'])}")
    print("================\n")
    
    # 清理
    analyzer.clear_analysis()

if __name__ == '__main__':
    analyze_single_file() 